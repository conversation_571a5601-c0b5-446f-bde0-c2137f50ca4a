// Background Service Worker - Orchestrates the entire arbitrage process
console.log('🚀 SERVICE WORKER LOADING AT', new Date().toISOString());
console.log('🔧 Service Worker Script Started - This should appear immediately!');

// Test if imports work
try {
  importScripts('../config/fee_structures.js', '../lib/profit-calculator.js');
  console.log('✅ Import scripts loaded successfully');
} catch (error) {
  console.error('❌ Import scripts failed:', error);
}

class ArbitrageOrchestrator {
  constructor() {
    this.isProcessing = false;
    this.currentBatch = [];
    this.results = [];
    this.profitCalculator = new ProfitCalculator();
    this.progress = { current: 0, total: 0, stage: 'idle' };

    this.scrapingFailureReason = null; // Track why scraping failed
  }

  async startArbitrageAnalysis(config) {
    if (this.isProcessing) {
      throw new Error('Analysis already in progress');
    }

    console.log('🚀 Starting arbitrage analysis with config:', config);
    console.log('🔓 Bypass filters enabled:', config.bypassFilters);
    this.isProcessing = true;
    this.results = [];
    this.progress = { current: 0, total: 100, stage: 'initializing' };

    try {
      // Step 1: Scrape eBay sold listings (40% of progress)
      DebugLogger.logAnalysisPhase('eBay Scraping', `Config: minSales=${config.minSales}, maxPages=${config.maxPages}, priceRange=${JSON.stringify(config.priceRange)}`);
      this.updateProgress(5, 'Scraping eBay sold listings...');
      this.scrapingFailureReason = null; // Reset failure reason

      const ebayProducts = await this.scrapeEbayListings(config);
      console.log(`✅ Found ${ebayProducts.length} qualifying eBay products`);
      DebugLogger.logAnalysisPhase('eBay Scraping Complete', `Products found: ${ebayProducts.length}, Failure reason: ${this.scrapingFailureReason || 'none'}`);

      // ⚡ RAPID TESTING: Log all raw scraped products immediately
      console.log('🔍 RAW SCRAPED PRODUCTS:', ebayProducts);
      if (ebayProducts.length > 0) {
        console.log('📋 PRODUCT TITLES:', ebayProducts.map(p => p.title).slice(0, 10));
        console.log('💰 PRODUCT PRICES:', ebayProducts.map(p => p.price).slice(0, 10));
      }

      // ⚡ RAPID TESTING: Raw debug mode bypasses all filtering and matching
      if (config.rawDebugMode || this.rawDebugMode) {
        console.log('⚡ RAW DEBUG MODE: Bypassing filtering and returning all scraped products');
        this.updateProgress(100, `Raw Debug Complete! Found ${ebayProducts.length} products (no filtering)`);

        return {
          success: true,
          summary: {
            totalScraped: ebayProducts.length,
            rawDebugMode: true,
            message: `Raw scraping test successful - found ${ebayProducts.length} products`
          },
          results: ebayProducts.slice(0, 50) // Return raw products for inspection
        };
      }

      if (ebayProducts.length === 0) {
        // If bypass filters is enabled, don't throw error for empty results
        if (config.bypassFilters) {
          console.log('🔓 Bypass filters enabled - continuing with empty results');
        } else {
          // Enhanced error differentiation
          if (this.scrapingFailureReason === 'BFCACHE_EXHAUSTED') {
            throw new Error('eBay scraping failed due to Chrome browser cache issues. Try: 1) Keep the eBay tab in focus during analysis, 2) Restart Chrome, 3) Disable other extensions temporarily, or 4) Use a fresh browser profile.');
          } else if (this.scrapingFailureReason === 'CONTENT_SCRIPT_FAILED') {
            throw new Error('eBay content script failed to load. Try: 1) Refresh the eBay page, 2) Reload the extension, 3) Check if you\'re on the correct eBay sold listings page.');
          } else if (this.scrapingFailureReason === 'COMMUNICATION_TIMEOUT') {
            throw new Error('eBay page communication timeout. Try: 1) Check your internet connection, 2) Reduce the number of pages to scan, 3) Try again in a few minutes.');
          } else {
            // Legitimate empty results - provide diagnostic suggestions
            const diagnosticSuggestions = this.generateDiagnosticSuggestions(config);
            throw new Error(`No qualifying eBay products found with current criteria. ${diagnosticSuggestions}`);
          }
        }
      }

      // Step 2: Skip Amazon matching for now - focus on scraping accuracy
      this.updateProgress(50, 'Skipping Amazon matching - focusing on scraping...');
      console.log('📍 Skipping Amazon matching to focus on scraping accuracy');

      // Convert scraped products to display format
      const scrapedProducts = ebayProducts.map((product, index) => ({
        ...product,
        displayIndex: index + 1,
        status: 'scraped',
        source: 'ebay-scraping'
      }));

      this.updateProgress(100, `Scraping complete! Found ${scrapedProducts.length} products`);
      this.results = scrapedProducts;

      return {
        success: true,
        summary: {
          totalAnalyzed: ebayProducts.length,
          opportunities: scrapedProducts.length, // Rename to match UI expectation
          totalScraped: scrapedProducts.length,
          avgPrice: scrapedProducts.length > 0 ?
            scrapedProducts.reduce((sum, p) => sum + (p.price || 0), 0) / scrapedProducts.length : 0,
          priceRange: scrapedProducts.length > 0 ? {
            min: Math.min(...scrapedProducts.map(p => p.price || 0)),
            max: Math.max(...scrapedProducts.map(p => p.price || 0))
          } : { min: 0, max: 0 }
        },
        results: scrapedProducts,
        mode: 'scraping-only'
      };

    } catch (error) {
      console.error('❌ Arbitrage analysis failed:', error);
      this.updateProgress(0, `Error: ${error.message}`);
      throw error;
    } finally {
      this.isProcessing = false;
      // Background analysis tabs to minimize user disruption
      await this.backgroundAnalysisTabs();
    }
  }

  async scrapeEbayListings(config) {
    console.log('📍 Starting eBay scraping with config:', config);
    try {
      // ✅ CRITICAL FIX: Get the current active tab instead of random eBay tab
      const [currentTab] = await chrome.tabs.query({ active: true, currentWindow: true });
      console.log('🔍 Current active tab:', currentTab?.url);

      let ebayTab;

      // Check if current tab is eBay, if so use it
      if (currentTab && currentTab.url.includes('ebay.com')) {
        ebayTab = currentTab;
        console.log('✅ Using current active eBay tab:', ebayTab.id);
        // Current tab is eBay, validate it's working
        if (await this.validateTab(ebayTab.id)) {
          console.log('✅ Current eBay tab is valid, proceeding with scraping');
        } else {
          console.log('❌ Current eBay tab invalid, cannot proceed');
          throw new Error('Current eBay tab is not responding');
        }
      } else {
        // Current tab is not eBay - this is an error for side panel usage
        console.log('❌ Current tab is not eBay:', currentTab?.url);
        throw new Error('Please navigate to an eBay search results page first. Current tab: ' + (currentTab?.url || 'unknown'));
      }

      return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('eBay scraping timeout'));
        }, 60000); // 1 minute timeout - more reasonable

        // Validate tab before sending message
        this.validateTab(ebayTab.id).then(isValid => {
          if (!isValid) {
            clearTimeout(timeout);
            reject(new Error('eBay tab became invalid'));
            return;
          }

          console.log('📤 Sending message to eBay tab with bfcache recovery:', ebayTab.id);

          // 🛡️ ENHANCED FIX: Force tab refresh and content script re-injection
          this.forceTabRefreshAndInject(ebayTab.id).then(() => {
            console.log('✅ Tab refreshed and content script re-injected, proceeding with scraping');

            // Use enhanced messaging with multiple recovery attempts
            return this.sendMessageWithEnhancedRecovery(ebayTab.id, {
              action: 'startScraping',
              config: {
                minSales: config.minSales || 3,
                daysBack: config.daysBack || 30,
                maxPages: config.maxPages || 10,
                priceRange: config.priceRange || { min: 10, max: 500 }
              }
            });
          }).then((response) => {
            clearTimeout(timeout);

            console.log('📥 Received response from eBay tab:', response);
            if (response?.success) {
              // Fix: Extract items from the correct response structure
              const products = response.products?.items || response.products || [];
              console.log(`✅ eBay scraping successful: ${products.length} products`);
              console.log('🔍 RAW SCRAPED PRODUCTS:', products);
              resolve(products);
            } else {
              console.error('❌ eBay scraping failed:', response?.error);
              reject(new Error(response?.error || 'eBay scraping failed'));
            }
          }).catch(error => {
            clearTimeout(timeout);

            // Enhanced graceful failure handling
            console.warn('⚠️ eBay scraping encountered an issue:', error.message);

            if (error.message === 'BFCACHE_RECOVERY_NEEDED' ||
                error.message.includes('message port closed') ||
                error.message.includes('Content script communication failed')) {
              console.warn('🔄 Returning empty results due to communication issues - analysis will continue');
              this.scrapingFailureReason = 'COMMUNICATION_FAILED';
              resolve([]); // Return empty array to continue analysis gracefully
            } else if (error.message.includes('timeout')) {
              console.warn('⏰ eBay scraping timeout - returning empty results to continue analysis');
              this.scrapingFailureReason = 'TIMEOUT';
              resolve([]); // Graceful degradation for timeouts
            } else {
              console.warn('🔄 Unknown error - returning empty results to continue analysis');
              this.scrapingFailureReason = 'UNKNOWN_ERROR';
              resolve([]); // Always continue gracefully
            }
          });
        }).catch(error => {
          clearTimeout(timeout);
          reject(error);
        });
      });
    } catch (error) {
      console.error('eBay scraping setup failed:', error);
      throw new Error(`eBay setup failed: ${error.message}`);
    }
  }

  async createEbayTab() {
    const ebayTab = await chrome.tabs.create({
      url: 'https://www.ebay.com/sch/i.html?_nkw=&LH_Sold=1&LH_Complete=1',
      active: true  // CRITICAL: Keep active to prevent bfcache during messaging
    });
    await this.waitForTabLoad(ebayTab.id);

    // Critical: Add delay for content script injection
    console.log('⏳ Waiting for content script injection...');
    await this.delay(3000);  // 3 seconds to ensure content script loads
    console.log('✅ Content script injection delay complete');

    // Verify content script injection
    const injectionVerified = await this.verifyContentScriptInjection(ebayTab.id);
    if (!injectionVerified) {
      console.warn('⚠️ Content script injection verification failed');
      this.scrapingFailureReason = 'CONTENT_SCRIPT_FAILED';
    }

    return ebayTab;
  }

  // Verify content script injection by sending a ping
  async verifyContentScriptInjection(tabId) {
    try {
      console.log('🔍 Verifying content script injection for tab:', tabId);

      const response = await new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Content script ping timeout'));
        }, 5000);

        chrome.tabs.sendMessage(tabId, { action: 'ping' }, (response) => {
          clearTimeout(timeout);

          if (chrome.runtime.lastError) {
            console.warn('❌ Content script ping failed:', chrome.runtime.lastError.message);
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            resolve(response);
          }
        });
      });

      if (response?.success) {
        console.log('✅ Content script injection verified');
        return true;
      } else {
        console.warn('❌ Content script ping returned unsuccessful response');
        return false;
      }
    } catch (error) {
      console.warn('❌ Content script injection verification failed:', error.message);
      return false;
    }
  }

  // Add delay utility method
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Background tabs after analysis completion to minimize user disruption
  async backgroundAnalysisTabs() {
    try {
      const ebayTabs = await chrome.tabs.query({ url: "*://*.ebay.com/*" });
      const amazonTabs = await chrome.tabs.query({ url: "*://*.amazon.com/*" });

      for (const tab of [...ebayTabs, ...amazonTabs]) {
        await chrome.tabs.update(tab.id, { active: false });
      }

      console.log('📱 Analysis tabs backgrounded to minimize user disruption');
    } catch (error) {
      console.warn('Failed to background analysis tabs:', error.message);
    }
  }

  // 🛡️ PRODUCTION FIX: Tab validation to prevent "No tab with id" errors
  async validateTabExists(tabId) {
    try {
      const tab = await chrome.tabs.get(tabId);
      return tab && !tab.discarded && tab.status !== 'unloaded';
    } catch (error) {
      console.log(`❌ Tab ${tabId} no longer exists:`, error.message);
      return false;
    }
  }

  // 🛡️ PRODUCTION FIX: Safe tab operations with validation and retry logic
  async safeTabOperation(operation, tabId, operationName = 'tab operation', maxRetries = 3) {
    const isValid = await this.validateTabExists(tabId);
    if (!isValid) {
      throw new Error(`Cannot perform ${operationName}: Tab ${tabId} no longer exists`);
    }

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        // Re-validate tab if operation failed
        const stillValid = await this.validateTabExists(tabId);
        if (!stillValid) {
          throw new Error(`Tab ${tabId} was closed during ${operationName}`);
        }

        // 🛡️ PRODUCTION FIX: Handle "Tabs cannot be edited" errors with retry
        if (error.message.includes('cannot be edited') && attempt < maxRetries) {
          const delay = attempt * 500; // Increasing delay: 500ms, 1000ms, 1500ms
          console.log(`⏳ Tab busy during ${operationName}, retrying in ${delay}ms (attempt ${attempt}/${maxRetries})...`);
          await this.delay(delay);
          continue;
        }

        // 🛡️ PRODUCTION FIX: Handle tab dragging/manipulation errors
        if (error.message.includes('user may be dragging') && attempt < maxRetries) {
          const delay = 1000; // Fixed 1 second delay for user interaction
          console.log(`👆 User is manipulating tabs, waiting ${delay}ms before retry (attempt ${attempt}/${maxRetries})...`);
          await this.delay(delay);
          continue;
        }

        // If this is the last attempt or a non-retryable error, throw it
        if (attempt === maxRetries) {
          console.error(`❌ ${operationName} failed after ${maxRetries} attempts:`, error.message);
        }
        throw error;
      }
    }
  }

  // 🛡️ PRODUCTION FIX: Specialized method for tab manipulation operations
  async safeTabManipulation(tabId, operation, operationName = 'tab manipulation') {
    // These operations are especially prone to "cannot be edited" errors
    // Use longer delays and more retries
    return await this.safeTabOperation(operation, tabId, operationName, 5);
  }

  // 🛡️ PRODUCTION FIX: Safe tab activation with user interaction handling
  async safeActivateTab(tabId) {
    return await this.safeTabManipulation(tabId, async () => {
      await chrome.tabs.update(tabId, { active: true });
    }, 'tab activation');
  }

  // 🛡️ PRODUCTION FIX: Safe window focus with user interaction handling
  async safeFocusWindow(windowId) {
    for (let attempt = 1; attempt <= 3; attempt++) {
      try {
        await chrome.windows.update(windowId, { focused: true });
        return;
      } catch (error) {
        if (error.message.includes('cannot be edited') && attempt < 3) {
          console.log(`⏳ Window busy, retrying focus in ${attempt * 500}ms...`);
          await this.delay(attempt * 500);
          continue;
        }
        throw error;
      }
    }
  }

  // 🛡️ PRODUCTION FIX: Enhanced content script verification with re-injection
  async ensureContentScriptReady(tabId, maxRetries = 3) {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`🔍 Checking content script readiness (attempt ${attempt}/${maxRetries})`);

        // Validate tab first
        const isValid = await this.validateTabExists(tabId);
        if (!isValid) {
          throw new Error(`Tab ${tabId} no longer exists for content script verification`);
        }

        // Try to ping content script
        const response = await this.safeTabOperation(async () => {
          return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
              reject(new Error('Content script ping timeout'));
            }, 5000);

            chrome.tabs.sendMessage(tabId, { action: 'ping' }, (response) => {
              clearTimeout(timeout);
              if (chrome.runtime.lastError) {
                reject(new Error(chrome.runtime.lastError.message));
              } else {
                resolve(response);
              }
            });
          });
        }, tabId, 'content script ping');

        if (response?.success && response?.message === 'pong') {
          console.log('✅ Content script is ready and responsive');

          // Log detailed initialization status
          if (response.initialization) {
            const init = response.initialization;
            console.log('📊 Content Script Status:');
            console.log(`  Integration Available: ${init.integrationAvailable}`);
            console.log(`  Integration Initialized: ${init.integrationInitialized}`);
            console.log(`  Initialization Attempts: ${init.initializationAttempts}`);
            console.log(`  Dependencies Ready: ${init.dependencies?.ready || false}`);

            if (!init.dependencies?.ready) {
              console.log(`  Missing Dependencies: ${init.dependencies?.missing?.join(', ') || 'unknown'}`);
            }

            // Warn if integration isn't ready
            if (!init.integrationInitialized) {
              console.warn('⚠️ Content script responding but enhanced integration not initialized');
              console.warn('   This may cause "Enhanced integration failed to initialize" errors');
            }
          }

          return true;
        }

        throw new Error('Content script not responding correctly');

      } catch (error) {
        console.warn(`❌ Content script check attempt ${attempt} failed:`, error.message);

        // Try programmatic injection if content script not responding
        if (attempt === 1) {
          console.log('🔧 Attempting programmatic content script injection');
          try {
            await chrome.scripting.executeScript({
              target: { tabId: tabId },
              files: [
                'config/fee_structures.js',
                'content/shared/event-bus.js',
                'content/shared/rate-limiter.js',
                'content/shared/error-handler.js',
                'lib/profit-calculator.js'
              ]
            });
            console.log('✅ Programmatic injection completed');
            // Wait a moment for scripts to initialize
            await new Promise(resolve => setTimeout(resolve, 1000));
            continue; // Retry the ping
          } catch (injectionError) {
            console.warn('❌ Programmatic injection failed:', injectionError.message);
          }
        }

        // Don't retry if tab no longer exists
        if (error.message.includes('no longer exists')) {
          throw error;
        }

        if (attempt < maxRetries) {
          console.log(`🔄 Attempting content script re-injection...`);

          try {
            await this.reinjectContentScript(tabId);
            await this.delay(2000); // Wait for injection to complete
          } catch (injectionError) {
            console.warn('❌ Content script re-injection failed:', injectionError.message);
            if (injectionError.message.includes('no longer exists')) {
              throw injectionError;
            }
          }
        }
      }
    }

    throw new Error(`Content script verification failed after ${maxRetries} attempts`);
  }

  // 🛡️ PRODUCTION FIX: Re-inject content scripts when they become unresponsive
  async reinjectContentScript(tabId) {
    const isValid = await this.validateTabExists(tabId);
    if (!isValid) {
      throw new Error(`Cannot re-inject: Tab ${tabId} no longer exists`);
    }

    try {
      // Get tab info to determine which scripts to inject
      const tab = await this.safeTabOperation(async () => {
        return await chrome.tabs.get(tabId);
      }, tabId, 'get tab for re-injection');

      if (tab.url.includes('ebay.')) {
        console.log('💉 Re-injecting eBay content scripts...');

        // Inject scripts in correct order
        await chrome.scripting.executeScript({
          target: { tabId: tabId },
          files: ['config/fee_structures.js']
        });

        await chrome.scripting.executeScript({
          target: { tabId: tabId },
          files: [
            'content/shared/event-bus.js',
            'content/shared/rate-limiter.js',
            'content/shared/error-handler.js',
            'lib/profit-calculator.js'
          ]
        });

        // ✅ STREAMLINED: No additional injection needed - universal handler has everything
        console.log('✅ Using streamlined universal handler - no additional scripts needed');

        console.log('✅ eBay content scripts re-injected successfully');

      } else if (tab.url.includes('amazon.')) {
        console.log('💉 Amazon content scripts not yet implemented (Phase 2)');
        // TODO: Implement Amazon content script injection in Phase 2
      } else {
        throw new Error(`Unsupported URL for content script injection: ${tab.url}`);
      }

    } catch (error) {
      console.error('❌ Content script re-injection failed:', error);
      throw new Error(`Content script re-injection failed: ${error.message}`);
    }
  }

  // Verify content script is loaded and responsive (handshake pattern)
  async verifyContentScriptHandshake(tabId, maxRetries = 3) {
    for (let attempt = 0; attempt < maxRetries; attempt++) {
      try {
        console.log(`🤝 Attempting content script handshake (attempt ${attempt + 1}/${maxRetries})`);

        // 🛡️ PRODUCTION FIX: Validate tab before messaging
        const isValid = await this.validateTabExists(tabId);
        if (!isValid) {
          throw new Error(`Tab ${tabId} no longer exists for handshake`);
        }

        const response = await this.safeTabOperation(async () => {
          return new Promise((resolve, reject) => {
            chrome.tabs.sendMessage(tabId, { action: 'ping' }, (response) => {
              if (chrome.runtime.lastError) {
                reject(new Error(chrome.runtime.lastError.message));
              } else {
                resolve(response);
              }
            });
          });
        }, tabId, 'content script handshake');

        if (response?.success && response?.message === 'pong') {
          console.log('✅ Content script handshake successful');
          return true;
        }
      } catch (error) {
        console.warn(`❌ Handshake attempt ${attempt + 1} failed:`, error.message);

        // Don't retry if tab no longer exists
        if (error.message.includes('no longer exists')) {
          throw error;
        }

        if (attempt < maxRetries - 1) {
          // Wait before retry, with increasing delay
          await this.delay(1000 * (attempt + 1));

          // Try to refresh the tab if content script isn't responding
          if (attempt === 1) {
            const stillValid = await this.validateTabExists(tabId);
            if (stillValid) {
              console.log('🔄 Refreshing tab to re-inject content script...');
              await this.safeTabOperation(async () => {
                await chrome.tabs.reload(tabId);
              }, tabId, 'tab reload');
              await this.waitForTabLoad(tabId);
              await this.delay(2000); // Wait for content script injection
            }
          }
        }
      }
    }

    throw new Error('Content script handshake failed after all retries');
  }

  // 🛡️ ENHANCED FIX: Force tab refresh and content script re-injection
  async forceTabRefreshAndInject(tabId) {
    try {
      console.log(`🔄 Force refreshing tab ${tabId} to clear bfcache issues`);

      // Get current tab URL before refresh
      const tab = await chrome.tabs.get(tabId);
      const currentUrl = tab.url;

      // Reload the tab to clear any bfcache issues
      await chrome.tabs.reload(tabId);

      // Wait for page to fully load
      await this.delay(4000);

      // Ensure we're on an eBay page
      if (!currentUrl.includes('ebay.com')) {
        console.log('🔄 Navigating to eBay sold listings page');
        await chrome.tabs.update(tabId, {
          url: 'https://www.ebay.com/sch/i.html?_nkw=iphone&_sacat=0&LH_Sold=1&_sop=13'
        });
        await this.delay(3000);
      }

      // Force activate the tab
      await this.safeActivateTab(tabId);
      await this.delay(1000);

      console.log('✅ Tab refresh and navigation complete');
      return true;
    } catch (error) {
      console.error('❌ Failed to refresh tab:', error);
      throw error;
    }
  }

  // 🛡️ ENHANCED FIX: Enhanced messaging with multiple recovery strategies
  async sendMessageWithEnhancedRecovery(tabId, message, maxRetries = 3) {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`📤 Enhanced messaging attempt ${attempt}/${maxRetries} to tab ${tabId}`);

        // Strategy 1: Direct messaging (fastest)
        if (attempt === 1) {
          return await this.attemptDirectMessage(tabId, message);
        }

        // Strategy 2: Refresh and retry (medium recovery)
        if (attempt === 2) {
          console.log('🔄 Strategy 2: Refreshing tab and retrying');
          await chrome.tabs.reload(tabId);
          await this.delay(3000);
          return await this.attemptDirectMessage(tabId, message);
        }

        // Strategy 3: Full reset (strongest recovery)
        if (attempt === 3) {
          console.log('🔄 Strategy 3: Full tab reset and content script re-injection');
          await this.forceTabRefreshAndInject(tabId);
          await this.delay(2000);
          return await this.attemptDirectMessage(tabId, message);
        }

      } catch (error) {
        console.warn(`⚠️ Enhanced messaging attempt ${attempt} failed:`, error.message);

        if (attempt === maxRetries) {
          // Final attempt failed - return graceful degradation
          console.error('❌ All enhanced messaging attempts failed, returning empty results');
          return { success: true, products: [], error: 'Content script communication failed' };
        }

        // Wait before next attempt
        await this.delay(1000 * attempt);
      }
    }
  }

  // Helper method for direct messaging attempts
  async attemptDirectMessage(tabId, message) {
    return new Promise((resolve, reject) => {
      console.log(`📤 Attempting direct message to tab ${tabId}:`, message.action);

      const timeout = setTimeout(() => {
        console.error(`⏰ Direct message timeout after 15s for tab ${tabId}`);
        reject(new Error('Direct message timeout'));
      }, 15000); // 15 second timeout

      chrome.tabs.sendMessage(tabId, message, (response) => {
        clearTimeout(timeout);

        if (chrome.runtime.lastError) {
          const errorMsg = chrome.runtime.lastError.message;
          console.error(`❌ Direct message error for tab ${tabId}:`, errorMsg);

          if (errorMsg.includes('message port closed') ||
              errorMsg.includes('receiving end does not exist') ||
              errorMsg.includes('back/forward cache')) {
            console.warn('🔄 Bfcache recovery needed');
            reject(new Error('BFCACHE_RECOVERY_NEEDED'));
          } else {
            reject(new Error(errorMsg));
          }
        } else if (response) {
          console.log(`✅ Direct message successful for tab ${tabId}:`, response);
          resolve(response);
        } else {
          console.warn(`⚠️ No response received from tab ${tabId}`);
          reject(new Error('No response received'));
        }
      });
    });
  }

  // bfcache recovery method - handles Chrome's back/forward cache issues
  async sendMessageWithRecovery(tabId, message, maxRetries = 5) {
    for (let attempt = 0; attempt < maxRetries; attempt++) {
      try {
        // 🛡️ PRODUCTION FIX: Validate tab before any operations
        const isValid = await this.validateTabExists(tabId);
        if (!isValid) {
          throw new Error(`Tab ${tabId} no longer exists for messaging`);
        }

        // 🛡️ PRODUCTION FIX: Use safer tab manipulation methods
        await this.safeActivateTab(tabId);

        const tab = await this.safeTabOperation(async () => {
          return await chrome.tabs.get(tabId);
        }, tabId, 'get tab info');

        if (tab.windowId) {
          await this.safeFocusWindow(tab.windowId);
        }
        await this.delay(750); // Increased delay for stronger focus

        const response = await new Promise((resolve, reject) => {
          chrome.tabs.sendMessage(tabId, message, (response) => {
            if (chrome.runtime.lastError) {
              const errorMsg = chrome.runtime.lastError.message;
              if (errorMsg.includes('back/forward cache') ||
                  errorMsg.includes('message channel is closed') ||
                  errorMsg.includes('receiving end does not exist')) {
                reject(new Error('BFCACHE_RECOVERY_NEEDED'));
              } else {
                reject(new Error(errorMsg));
              }
            } else {
              resolve(response);
            }
          });
        });

        // Keep tab active during batch operations - don't background automatically
        // Tab will be backgrounded manually when entire session completes
        return response;

      } catch (error) {
        if (error.message === 'BFCACHE_RECOVERY_NEEDED' && attempt < maxRetries - 1) {
          DebugLogger.logBfcacheRecovery(tabId, attempt + 1, maxRetries, message);

          // Reload tab to recover from bfcache with stronger recovery
          console.log('🔄 Reloading tab with cache bypass...');
          await chrome.tabs.reload(tabId, { bypassCache: true });
          await this.waitForTabLoad(tabId);
          await this.delay(4000); // Increased delay for content script re-injection

          // Stronger tab activation
          console.log('💪 Applying stronger tab activation...');
          await chrome.tabs.update(tabId, { active: true });
          const tab = await chrome.tabs.get(tabId);
          if (tab.windowId) {
            await chrome.windows.update(tab.windowId, { focused: true });
          }
          await this.delay(1000); // Additional delay after recovery

          // Verify content script injection after recovery
          const injectionVerified = await this.verifyContentScriptInjection(tabId);
          DebugLogger.logContentScriptStatus(tabId, injectionVerified ? 'success' : 'failed',
            injectionVerified ? 'Content script responsive after recovery' : 'Content script still unresponsive');

          continue;
        }

        // Enhanced error logging when recovery is exhausted
        if (error.message === 'BFCACHE_RECOVERY_NEEDED') {
          console.error(`❌ bfcache recovery exhausted after ${maxRetries} attempts for tab ${tabId}`);
          console.error(`📊 Failed message: ${JSON.stringify(message)}`);

          try {
            const tab = await chrome.tabs.get(tabId);
            console.error(`🔍 Final tab state: active=${tab.active}, status=${tab.status}, url=${tab.url}`);
          } catch (tabError) {
            console.error(`🔍 Could not get final tab state: ${tabError.message}`);
          }

          // Provide user guidance
          const userGuidance = [
            'Keep the eBay tab in focus during analysis',
            'Try restarting Chrome browser',
            'Disable other extensions temporarily',
            'Use Chrome in incognito mode',
            'Try reducing the number of pages to scan'
          ];
          DebugLogger.logUserGuidance('BFCACHE_EXHAUSTED', userGuidance);
        }

        throw error;
      }
    }
  }

  async findAmazonMatches(ebayProducts, config) {
    const matches = [];
    const batchSize = 3; // Small batches to avoid rate limiting
    const maxMatches = config.maxMatches || 2;

    for (let i = 0; i < ebayProducts.length; i += batchSize) {
      const batch = ebayProducts.slice(i, i + batchSize);
      
      // Update progress
      const progressPercent = 45 + (i / ebayProducts.length) * 35;
      this.updateProgress(progressPercent, `Finding matches... ${i + 1}/${ebayProducts.length}`);
      
      const batchMatches = await Promise.allSettled(
        batch.map(product => this.findSingleMatch(product, maxMatches))
      );
      
      // Process results
      batchMatches.forEach((result, index) => {
        if (result.status === 'fulfilled' && result.value) {
          matches.push(result.value);
        } else {
          console.warn(`Failed to find match for: ${batch[index].title}`);
        }
      });
      
      // Rate limiting between batches
      if (i + batchSize < ebayProducts.length) {
        await this.delay(5000); // 5 second delay between batches
      }
    }

    return matches.filter(match => match !== null);
  }

  async findSingleMatch(ebayProduct, maxMatches) {
    try {
      // Create or find Amazon tab
      let amazonTab = await this.getOrCreateAmazonTab();

      // Validate tab before sending message
      if (!(await this.validateTab(amazonTab.id))) {
        console.warn('Amazon tab invalid, creating new one');
        amazonTab = await this.getOrCreateAmazonTab();
      }

      // Use bfcache recovery method for reliable Amazon messaging
      const response = await Promise.race([
        this.sendMessageWithRecovery(amazonTab.id, {
          action: 'findMatches',
          ebayProduct,
          maxResults: maxMatches
        }),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Amazon search timeout')), 30000)
        )
      ]);

      if (response?.success && response.matches.length > 0) {
        return {
          ebayProduct,
          amazonMatches: response.matches
        };
      }

      return null;
    } catch (error) {
      // Graceful failure handling for bfcache issues
      if (error.message === 'BFCACHE_RECOVERY_NEEDED') {
        console.warn('⚠️ Amazon matching skipped due to unrecoverable bfcache issue for:', ebayProduct.title);
        return null; // Skip this product, continue with others
      } else if (error.message === 'Amazon search timeout') {
        console.warn('⏰ Amazon search timeout for:', ebayProduct.title);
        return null; // Skip this product, continue with others
      } else {
        console.warn('Failed to find match for:', ebayProduct.title, error.message);
        return null;
      }
    }
  }

  async getOrCreateAmazonTab() {
    try {
      const amazonTabs = await chrome.tabs.query({ url: "*://*.amazon.com/*" });

      if (amazonTabs.length > 0) {
        // Validate existing tab
        const existingTab = amazonTabs[0];
        if (await this.validateTab(existingTab.id)) {
          return existingTab;
        }
      }

      // Create new tab if none exist or existing is invalid
      const tab = await chrome.tabs.create({
        url: 'https://www.amazon.com/s?k=test',
        active: true  // CRITICAL: Keep active to prevent bfcache during messaging
      });
      await this.waitForTabLoad(tab.id);

      // Add delay for content script injection
      console.log('⏳ Waiting for Amazon content script injection...');
      await this.delay(2000);  // 2 seconds for Amazon
      console.log('✅ Amazon content script injection delay complete');

      return tab;
    } catch (error) {
      console.error('Failed to create Amazon tab:', error);
      throw new Error(`Amazon tab creation failed: ${error.message}`);
    }
  }

  async validateTab(tabId) {
    // 🛡️ PRODUCTION FIX: Use enhanced validation
    return await this.validateTabExists(tabId);
  }

  calculateProfits(matches, config) {
    const calculations = [];
    
    matches.forEach(match => {
      match.amazonMatches.forEach(amazonProduct => {
        const profit = this.profitCalculator.calculateOpportunity(
          match.ebayProduct,
          amazonProduct,
          config.profitOptions || {}
        );
        
        calculations.push({
          ebayProduct: match.ebayProduct,
          amazonProduct,
          profitAnalysis: profit
        });
      });
    });
    
    return calculations;
  }

  filterOpportunities(calculations, config) {
    const criteria = {
      minProfit: config.minProfit || 10,
      minMargin: config.minMargin || 15,
      maxRisk: config.maxRisk || 70,
      minDemand: config.minDemand || 30,
      minGrade: config.minGrade || 'C'
    };
    
    const filtered = this.profitCalculator.filterOpportunities(calculations, criteria);
    
    // Sort by profit potential
    return filtered.sort((a, b) => {
      const scoreA = a.profitAnalysis.grossProfit * 0.6 + a.profitAnalysis.demandScore * 0.4;
      const scoreB = b.profitAnalysis.grossProfit * 0.6 + b.profitAnalysis.demandScore * 0.4;
      return scoreB - scoreA;
    });
  }

  updateProgress(percent, message) {
    this.progress = {
      current: Math.round(percent),
      total: 100,
      stage: message
    };
    
    // Broadcast progress to all connected popups
    chrome.runtime.sendMessage({
      type: 'PROGRESS_UPDATE',
      progress: this.progress
    }).catch(() => {}); // Ignore if no listeners
  }

  async waitForTabLoad(tabId) {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        chrome.tabs.onUpdated.removeListener(listener);
        reject(new Error('Tab load timeout'));
      }, 30000); // 30 second timeout

      const listener = (updatedTabId, changeInfo) => {
        if (updatedTabId === tabId && changeInfo.status === 'complete') {
          chrome.tabs.onUpdated.removeListener(listener);
          clearTimeout(timeout);

          // Validate tab still exists after loading
          this.validateTab(tabId).then(isValid => {
            if (isValid) {
              setTimeout(resolve, 2000); // Additional delay for content scripts
            } else {
              reject(new Error('Tab became invalid after loading'));
            }
          });
        }
      };

      chrome.tabs.onUpdated.addListener(listener);

      // Check if tab is already loaded
      chrome.tabs.get(tabId, (tab) => {
        if (chrome.runtime.lastError) {
          chrome.tabs.onUpdated.removeListener(listener);
          clearTimeout(timeout);
          reject(new Error('Tab not found'));
        } else if (tab.status === 'complete') {
          chrome.tabs.onUpdated.removeListener(listener);
          clearTimeout(timeout);
          setTimeout(resolve, 2000);
        }
      });
    });
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  getResults() {
    return {
      results: this.results,
      isProcessing: this.isProcessing,
      progress: this.progress
    };
  }

  stopAnalysis() {
    this.isProcessing = false;
    this.updateProgress(0, 'Analysis stopped by user');
    console.log('🛑 Analysis stopped by user');
  }

  // Generate diagnostic suggestions for empty results
  generateDiagnosticSuggestions(config) {
    const suggestions = [];

    // Check if criteria might be too strict
    if (config.minSales > 3) {
      suggestions.push(`Try reducing minimum sales from ${config.minSales} to 3`);
    }

    if (config.priceRange && (config.priceRange.max - config.priceRange.min) < 100) {
      suggestions.push(`Expand price range (currently $${config.priceRange.min}-$${config.priceRange.max})`);
    }

    if (config.maxPages < 5) {
      suggestions.push(`Increase pages to scan from ${config.maxPages} to 10+`);
    }

    // Add diagnostic mode suggestion
    suggestions.push('Try diagnostic mode: Set min sales=1, price range=$1-$1000, max pages=2');

    return suggestions.length > 0
      ? `Suggestions: ${suggestions.join(', ')}.`
      : 'Try broadening your search criteria or checking different product categories.';
  }



  // Get diagnostic configuration
  getDiagnosticConfig() {
    return {
      minSales: 1,
      daysBack: 1,  // ⚡ RAPID TESTING: Only 1 day back
      maxPages: 1,  // ⚡ RAPID TESTING: Only 1 page
      priceRange: { min: 1, max: 10000 },  // ⚡ RAPID TESTING: Wide open price range
      profitOptions: {
        minProfit: 1,
        minMargin: 1
      },
      rawDebugMode: true  // ⚡ RAPID TESTING: Bypass all filtering
    };
  }

  // Enable raw debug mode for rapid testing
  enableRawDebugMode() {
    this.rawDebugMode = true;
    console.log('⚡ RAW DEBUG MODE ENABLED - Bypassing all filtering for rapid testing');
    DebugLogger.logAnalysisPhase('Raw Debug Mode Enabled', 'Will show ALL scraped products without filtering');
  }

  // Test eBay connection and content script communication
  async testEbayConnection() {
    try {
      console.log('🔧 Starting eBay connection test...');

      // Step 1: Test tab creation
      console.log('📋 Step 1: Testing eBay tab creation...');
      const ebayTab = await this.getOrCreateEbayTab();
      console.log('✅ eBay tab created successfully:', ebayTab.id);

      // Step 2: Test content script communication
      console.log('📋 Step 2: Testing content script communication...');
      const response = await this.attemptDirectMessage(ebayTab.id, { action: 'ping' });
      console.log('✅ Content script communication successful:', response);

      // Step 3: Test enhanced recovery system
      console.log('📋 Step 3: Testing enhanced recovery system...');
      const enhancedResponse = await this.sendMessageWithEnhancedRecovery(ebayTab.id, { action: 'ping' });
      console.log('✅ Enhanced recovery system working:', enhancedResponse);

      return {
        success: true,
        message: 'All connection tests passed',
        details: {
          tabCreation: true,
          contentScriptCommunication: true,
          enhancedRecovery: true,
          tabId: ebayTab.id
        }
      };

    } catch (error) {
      console.error('❌ eBay connection test failed:', error);
      return {
        success: false,
        error: error.message,
        details: 'One or more connection tests failed'
      };
    }
  }
}

// Initialize orchestrator
let arbitrageOrchestrator;
try {
  arbitrageOrchestrator = new ArbitrageOrchestrator();
  console.log('✅ ArbitrageOrchestrator initialized successfully');
} catch (error) {
  console.error('❌ ArbitrageOrchestrator initialization failed:', error);
}

// Handle extension icon click to open side panel
chrome.action.onClicked.addListener(async (tab) => {
  try {
    await chrome.sidePanel.open({ tabId: tab.id });
    console.log('✅ Side panel opened');
  } catch (error) {
    console.error('❌ Failed to open side panel:', error);
  }
});

// Handle messages from popup and content scripts
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  console.log('🚨 SERVICE WORKER RECEIVED MESSAGE:', JSON.stringify(request));
  console.log('📨 Message details - Action:', request.action, 'Type:', request.type);

  try {
    // Test ping for service worker connectivity
    if (request.action === 'ping') {
      console.log('🏓 Service worker received ping, responding with pong');
      sendResponse({ success: true, message: 'pong', timestamp: Date.now() });
      return true;
    }

    // Test connection - comprehensive connectivity test
    if (request.action === 'testConnection') {
      console.log('🔧 Service worker received connection test request');

      // Test eBay tab creation and communication
      arbitrageOrchestrator.testEbayConnection()
        .then(result => {
          console.log('✅ Connection test completed:', result);
          sendResponse(result);
        })
        .catch(error => {
          console.error('❌ Connection test failed:', error);
          sendResponse({
            success: false,
            error: error.message,
            details: 'Service worker connection test failed'
          });
        });
      return true;
    }

    if (request.action === 'startAnalysis') {
      arbitrageOrchestrator.startArbitrageAnalysis(request.config)
        .then(result => {
          try {
            sendResponse(result);
          } catch (error) {
            console.warn('Failed to send analysis result:', error.message);
          }
        })
        .catch(error => {
          console.error('Analysis failed:', error);
          try {
            sendResponse({ success: false, error: error.message });
          } catch (sendError) {
            console.warn('Failed to send error response:', sendError.message);
          }
        });
      return true; // Keep message channel open
    }



    if (request.action === 'getResults') {
      try {
        sendResponse({
          success: true,
          ...arbitrageOrchestrator.getResults()
        });
      } catch (error) {
        console.warn('Failed to send results:', error.message);
        sendResponse({ success: false, error: 'Failed to retrieve results' });
      }
    }

    if (request.action === 'stopAnalysis') {
      try {
        arbitrageOrchestrator.stopAnalysis();
        sendResponse({ success: true });
      } catch (error) {
        console.warn('Failed to stop analysis:', error.message);
        sendResponse({ success: false, error: error.message });
      }
    }

    // Handle progress updates from content scripts
    if (request.type === 'SCRAPING_PROGRESS') {
      // Forward progress to popup if needed
      chrome.runtime.sendMessage(request).catch(() => {
        // Silently ignore if no popup is listening
      });
    }
  } catch (error) {
    console.error('Message handler error:', error);
    try {
      sendResponse({ success: false, error: 'Internal message handling error' });
    } catch (sendError) {
      console.warn('Failed to send error response:', sendError.message);
    }
  }
});

// Handle extension startup
chrome.runtime.onStartup.addListener(() => {
  console.log('🚀 eBay Arbitrage Researcher Pro started');
});

chrome.runtime.onInstalled.addListener(async () => {
  console.log('✅ eBay Arbitrage Researcher Pro installed');

  // ⚡ CRITICAL FIX: Re-inject content scripts on extension install/update
  console.log('🔧 Re-injecting content scripts into existing tabs...');

  try {
    const tabs = await chrome.tabs.query({});
    let injectionCount = 0;

    for (const tab of tabs) {
      // Skip chrome:// and other restricted pages
      if (tab.url.startsWith('chrome://') ||
          tab.url.startsWith('chrome-extension://') ||
          tab.url.startsWith('moz-extension://') ||
          tab.url.startsWith('edge://')) {
        continue;
      }

      try {
        // Check if tab matches our target URLs
        if (tab.url.includes('ebay.com')) {
          console.log(`💉 Injecting content scripts into eBay tab ${tab.id}: ${tab.url}`);

          await chrome.scripting.executeScript({
            target: { tabId: tab.id },
            files: [
              'content/shared/event-bus.js',
              'content/shared/error-handler.js',
              'content/shared/rate-limiter.js',
              'lib/profit-calculator.js'
            ]
          });

          injectionCount++;
          console.log(`✅ Successfully injected into eBay tab ${tab.id}`);
        }

        // TODO: Add Amazon injection in Phase 2
        // if (tab.url.includes('amazon.com')) { ... }

      } catch (error) {
        console.log(`⚠️ Failed to inject into tab ${tab.id} (${tab.url}):`, error.message);
        // Continue with other tabs even if one fails
      }
    }

    console.log(`✅ Content script re-injection complete: ${injectionCount} tabs processed`);

  } catch (error) {
    console.error('❌ Content script re-injection failed:', error);
  }
});

console.log('✅ SERVICE WORKER FULLY LOADED AND READY');

// Enhanced debugging utilities
class DebugLogger {
  static logBfcacheRecovery(tabId, attempt, maxRetries, message) {
    console.log(`🔄 bfcache Recovery Attempt ${attempt}/${maxRetries} for tab ${tabId}`);
    console.log(`📊 Message: ${message.action}, Config: ${JSON.stringify(message.config || {}, null, 2)}`);
    console.log(`⏰ Timestamp: ${new Date().toISOString()}`);
  }

  static logContentScriptStatus(tabId, status, details = '') {
    const statusIcon = status === 'success' ? '✅' : status === 'failed' ? '❌' : '⚠️';
    console.log(`${statusIcon} Content Script Status for tab ${tabId}: ${status}`);
    if (details) console.log(`📝 Details: ${details}`);
  }

  static logAnalysisPhase(phase, details = '') {
    console.log(`📍 Analysis Phase: ${phase}`);
    if (details) console.log(`📝 ${details}`);
  }

  static logUserGuidance(errorType, suggestions) {
    console.log(`🎯 User Guidance for ${errorType}:`);
    suggestions.forEach((suggestion, index) => {
      console.log(`   ${index + 1}. ${suggestion}`);
    });
  }

  static logDiagnosticInfo(config, results) {
    console.log('🔧 DIAGNOSTIC MODE RESULTS:');
    console.log('📊 Configuration used:', JSON.stringify(config, null, 2));
    console.log('📈 Results summary:', {
      ebayProductsFound: results.ebayProducts || 0,
      amazonMatchesFound: results.amazonMatches || 0,
      opportunitiesFound: results.opportunities || 0,
      scrapingFailureReason: results.scrapingFailureReason || 'none'
    });
  }
}

// Make DebugLogger globally available
self.DebugLogger = DebugLogger;
console.log('🎯 Service Worker Status: ACTIVE and waiting for messages');

// Force service worker to stay active for testing
self.addEventListener('install', event => {
  console.log('🔧 Service Worker INSTALL event');
  self.skipWaiting();
});

self.addEventListener('activate', event => {
  console.log('🔧 Service Worker ACTIVATE event');
  event.waitUntil(self.clients.claim());
});
