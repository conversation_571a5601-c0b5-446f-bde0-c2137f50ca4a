// eBay Arbitrage Researcher Pro - Popup Interface
class ArbitrageUI {
  constructor() {
    this.results = [];
    this.isProcessing = false;
    this.currentSort = 'profit';
    this.currentGradeFilter = 'all';

    this.initializeEventListeners();
    this.loadPreviousResults();
    this.startProgressPolling();

    // ✅ Check current tab status on popup load
    this.updateTabStatus();

    // ✅ Listen for tab changes to update status
    if (chrome.tabs && chrome.tabs.onActivated) {
      chrome.tabs.onActivated.addListener(() => {
        this.updateTabStatus();
      });
    }
  }

  initializeEventListeners() {
    // Helper function to safely add event listeners
    const safeAddEventListener = (id, event, handler) => {
      const element = document.getElementById(id);
      if (element) {
        element.addEventListener(event, handler);
      } else {
        console.warn(`Element with ID '${id}' not found`);
      }
    };

    // Main action buttons
    safeAddEventListener('startAnalysis', 'click', () => {
      this.startAnalysis();
    });

    safeAddEventListener('stopAnalysis', 'click', () => {
      this.stopAnalysis();
    });



    safeAddEventListener('exportResults', 'click', () => {
      this.exportToCSV();
    });

    safeAddEventListener('refreshResults', 'click', () => {
      this.loadPreviousResults();
    });

    safeAddEventListener('viewFullResults', 'click', () => {
      this.viewFullResults();
    });



    // Config toggle
    safeAddEventListener('configToggle', 'click', () => {
      this.toggleAdvancedConfig();
    });

    // Bypass filters toggle
    safeAddEventListener('bypassFilters', 'change', (e) => {
      this.handleBypassFiltersToggle(e.target.checked);
    });

    // Filter controls
    safeAddEventListener('sortBy', 'change', (e) => {
      this.currentSort = e.target.value;
      this.displayResults();
    });

    safeAddEventListener('gradeFilter', 'change', (e) => {
      this.currentGradeFilter = e.target.value;
      this.displayResults();
    });

    // Footer links
    safeAddEventListener('helpLink', 'click', (e) => {
      e.preventDefault();
      this.showHelp();
    });

    safeAddEventListener('settingsLink', 'click', (e) => {
      e.preventDefault();
      this.showSettings();
    });

    safeAddEventListener('aboutLink', 'click', (e) => {
      e.preventDefault();
      this.showAbout();
    });

    // Input validation
    this.setupInputValidation();
  }

  toggleAdvancedConfig() {
    const content = document.getElementById('configContent');
    const icon = document.querySelector('.toggle-icon');

    if (content.style.display === 'none') {
      content.style.display = 'block';
      icon.classList.add('expanded');
    } else {
      content.style.display = 'none';
      icon.classList.remove('expanded');
    }
  }

  handleBypassFiltersToggle(enabled) {
    console.log('🔓 Bypass filters toggled:', enabled);

    if (enabled) {
      // Set to scrape everything mode
      document.getElementById('minSales').value = 0;
      document.getElementById('minProfit').value = 0;
      document.getElementById('minMargin').value = 0;
      document.getElementById('maxRisk').value = 100;
      document.getElementById('minPrice').value = 1;
      document.getElementById('maxPrice').value = 10000;

      this.showNotification('🔓 Filters bypassed - will scrape everything!', 'info');
    } else {
      // Reset to default values
      document.getElementById('minSales').value = 3;
      document.getElementById('minProfit').value = 10;
      document.getElementById('minMargin').value = 15;
      document.getElementById('maxRisk').value = 70;
      document.getElementById('minPrice').value = 10;
      document.getElementById('maxPrice').value = 500;

      this.showNotification('🔒 Filters restored to defaults', 'info');
    }
  }

  setupInputValidation() {
    const inputs = document.querySelectorAll('input[type="number"]');
    inputs.forEach(input => {
      input.addEventListener('change', () => {
        const min = parseInt(input.getAttribute('min'));
        const value = parseInt(input.value);

        if (value < min) input.value = min;
        // Removed max validation to allow unlimited values
      });
    });
  }

  async updateTabStatus() {
    const statusElement = document.getElementById('tabStatus');
    const statusText = document.getElementById('tabStatusText');

    try {
      const [currentTab] = await chrome.tabs.query({ active: true, currentWindow: true });

      if (!currentTab) {
        statusElement.className = 'tab-status invalid';
        statusText.textContent = 'No active tab found';
        return;
      }

      if (!currentTab.url.includes('ebay.com')) {
        statusElement.className = 'tab-status invalid';
        statusText.textContent = 'Not on eBay - Navigate to eBay search results';
        return;
      }

      if (!currentTab.url.includes('/sch/')) {
        statusElement.className = 'tab-status invalid';
        statusText.textContent = 'Not on eBay search results - Navigate to search page';
        return;
      }

      // Valid eBay search page
      statusElement.className = 'tab-status valid';
      statusText.textContent = '✅ Ready - eBay search results page detected';

    } catch (error) {
      console.error('❌ Tab status check failed:', error);
      statusElement.className = 'tab-status invalid';
      statusText.textContent = 'Could not access current tab';
    }
  }

  async startAnalysis() {
    if (this.isProcessing) return;

    // ✅ CRITICAL: Validate current tab is eBay before starting
    console.log('🔍 Checking current tab...');
    try {
      const [currentTab] = await chrome.tabs.query({ active: true, currentWindow: true });
      console.log('📍 Current tab URL:', currentTab?.url);

      if (!currentTab || !currentTab.url.includes('ebay.com')) {
        this.showNotification('❌ Please navigate to an eBay search results page first', 'error');
        this.updateStatusIndicator('error', 'Not on eBay page');
        return;
      }

      if (!currentTab.url.includes('/sch/')) {
        this.showNotification('❌ Please navigate to an eBay search results page (not product page)', 'error');
        this.updateStatusIndicator('error', 'Not on search results');
        return;
      }

      console.log('✅ Current tab is valid eBay search page');
    } catch (error) {
      console.error('❌ Tab validation failed:', error);
      this.showNotification('❌ Could not access current tab', 'error');
      this.updateStatusIndicator('error', 'Tab access error');
      return;
    }

    // Update status indicator
    this.updateStatusIndicator('processing', 'Starting analysis...');

    // Test service worker connectivity
    console.log('🔍 Testing service worker connectivity...');
    try {
      const pingResponse = await this.sendMessage({ action: 'ping' });
      console.log('✅ Service worker ping successful:', pingResponse);
    } catch (error) {
      console.error('❌ Service worker ping failed:', error);
      this.showNotification('❌ Service worker not responding. Try reloading the extension.', 'error');
      this.updateStatusIndicator('error', 'Service worker error');
      return;
    }

    const config = this.getConfiguration();

    // Validate configuration
    if (!this.validateConfiguration(config)) {
      this.updateStatusIndicator('ready', 'Ready to start');
      return;
    }

    this.setProcessingState(true);
    this.showProgressSection();
    this.updateProgress(0, 'Initializing analysis...');

    try {
      console.log('🚀 Starting analysis with config:', config);
      
      const response = await this.sendMessage({ 
        action: 'startAnalysis', 
        config 
      });
      
      if (response.success) {
        this.results = response.results || [];
        this.displayResults();
        this.displaySummaryStats(response.summary);

        const count = response.summary.opportunities || response.results.length;
        const mode = response.mode || 'analysis';

        if (mode === 'scraping-only') {
          this.updateProgress(100, `Scraping complete! Found ${count} products.`);
          this.showNotification(`✅ Scraped ${count} products from eBay!`, 'success');
          this.updateStatusIndicator('ready', `Scraped ${count} products`);
        } else {
          this.updateProgress(100, `Analysis complete! Found ${count} opportunities.`);
          this.showNotification(`✅ Found ${count} profitable opportunities!`, 'success');
          this.updateStatusIndicator('ready', `Found ${count} opportunities`);
        }

        document.getElementById('exportResults').disabled = false;
      } else {
        throw new Error(response.error || 'Analysis failed');
      }
    } catch (error) {
      console.error('❌ Analysis failed:', error);
      this.updateProgress(0, `Error: ${error.message}`);
      this.showNotification(`❌ Analysis failed: ${error.message}`, 'error');
      this.updateStatusIndicator('error', 'Analysis failed');
    } finally {
      this.setProcessingState(false);
    }
  }

  async stopAnalysis() {
    try {
      await this.sendMessage({ action: 'stopAnalysis' });
      this.setProcessingState(false);
      this.updateProgress(0, 'Analysis stopped by user');
      this.updateStatusIndicator('ready', 'Analysis stopped');
      this.showNotification('⏹️ Analysis stopped', 'info');
    } catch (error) {
      console.error('Failed to stop analysis:', error);
      this.updateStatusIndicator('error', 'Stop failed');
    }
  }



  viewFullResults() {
    if (!this.results || this.results.length === 0) {
      this.showNotification('❌ No results to view. Run analysis first.', 'error');
      return;
    }

    // Create a detailed HTML page with all results
    const htmlContent = this.generateFullResultsHTML();

    // Create a blob and open in new tab
    const blob = new Blob([htmlContent], { type: 'text/html' });
    const url = URL.createObjectURL(blob);

    chrome.tabs.create({ url: url });
    this.showNotification('🔍 Full results opened in new tab', 'info');
  }

  generateFullResultsHTML() {
    const timestamp = new Date().toLocaleString();
    const count = this.results.length;

    let html = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>eBay Scraped Products - ${timestamp}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .header { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .product { background: white; margin: 10px 0; padding: 15px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .product-title { font-weight: bold; color: #333; margin-bottom: 8px; }
        .product-price { color: #e74c3c; font-size: 18px; font-weight: bold; }
        .product-link { color: #3498db; text-decoration: none; }
        .product-link:hover { text-decoration: underline; }
        .product-meta { color: #666; font-size: 12px; margin-top: 8px; }
        .stats { display: flex; gap: 20px; margin-bottom: 20px; }
        .stat { background: #3498db; color: white; padding: 10px 15px; border-radius: 6px; text-align: center; }
    </style>
</head>
<body>
    <div class="header">
        <h1>📦 eBay Scraped Products</h1>
        <p>Generated: ${timestamp}</p>
        <div class="stats">
            <div class="stat">
                <div>Total Products</div>
                <div><strong>${count}</strong></div>
            </div>
        </div>
    </div>
`;

    this.results.forEach((product, index) => {
      const totalCost = this.calculateTotalCost(product);

      html += `
    <div class="product">
        ${product.imageUrl ? `<div style="float: left; margin-right: 15px; margin-bottom: 10px;"><img src="${product.imageUrl}" alt="Product Image" style="max-width: 80px; max-height: 80px; object-fit: cover; border-radius: 4px;"></div>` : ''}

        <div class="product-header" style="margin-bottom: 8px;">
            <span style="font-size: 18px; font-weight: bold;">${index + 1}. ${this.escapeHtml(product.title || 'Unknown Product')}</span>
            ${product.condition ? `<span style="background: #f39c12; color: white; padding: 3px 8px; border-radius: 4px; font-size: 12px; margin-left: 10px;">${product.condition}</span>` : ''}
            ${product.listingType ? `<span style="background: ${product.listingType === 'Auction' ? '#e74c3c' : '#27ae60'}; color: white; padding: 3px 8px; border-radius: 4px; font-size: 12px; margin-left: 6px;">${product.listingType}</span>` : ''}
        </div>

        <div class="product-price">
            <div style="font-size: 18px; font-weight: bold; color: #2c3e50; margin-bottom: 5px;">
                Price: ${this.formatPrice(product.price)}
            </div>
            <div style="font-size: 16px; margin-bottom: 5px;">
                ${this.formatShippingInfo(product)}
            </div>
            <div style="font-size: 18px; font-weight: bold; color: #e74c3c; border-top: 2px solid #ecf0f1; padding-top: 8px; margin-top: 8px;">
                Total Cost: ${this.formatPrice(totalCost)}
            </div>
        </div>

        <div class="product-details" style="margin-top: 12px; padding: 10px; background: #f8f9fa; border-radius: 6px;">
            ${product.seller ? `
                <div style="margin-bottom: 6px; font-size: 14px;">
                    👤 <strong>Seller:</strong> <a href="https://www.ebay.com/usr/${product.seller}" target="_blank" style="color: #3498db; text-decoration: none; font-weight: bold;">${product.seller}</a>
                    ${product.sellerFeedback ? `<span style="color: #27ae60; font-weight: bold; margin-left: 8px;">(${product.sellerFeedback} feedback)</span>` : ''}
                </div>
            ` : ''}
            ${product.bids ? `<div style="margin-bottom: 6px; font-size: 14px;">🔨 <strong>Bidding:</strong> ${product.bids}</div>` : ''}
            ${product.location ? `<div style="margin-bottom: 6px; font-size: 14px;">📍 <strong>Location:</strong> ${product.location}</div>` : ''}
            ${product.soldDate ? `<div style="margin-bottom: 6px; font-size: 14px;">📅 <strong>Sold Date:</strong> ${product.soldDate}</div>` : ''}
            ${product.badges && product.badges.length > 0 ? `<div style="margin-bottom: 6px; font-size: 14px;">🏷️ <strong>Features:</strong> ${product.badges.join(', ')}</div>` : ''}
            ${product.category ? `<div style="margin-bottom: 6px; font-size: 14px;">📂 <strong>Category:</strong> ${product.category}</div>` : ''}
        </div>

        ${product.link ? `<div style="margin-top: 12px;"><a href="${product.link}" target="_blank" class="product-link" style="background: #3498db; color: white; padding: 8px 16px; border-radius: 4px; text-decoration: none; display: inline-block;">🔗 View on eBay</a></div>` : ''}

        <div class="product-meta" style="margin-top: 12px; font-size: 12px; color: #7f8c8d; border-top: 1px solid #ecf0f1; padding-top: 8px;">
            <strong>Source:</strong> ${product.source || 'unknown'} |
            <strong>Item ID:</strong> ${product.itemId || 'N/A'} |
            <strong>Scraped:</strong> ${new Date(product.timestamp || Date.now()).toLocaleString()}
        </div>
        <div style="clear: both;"></div>
    </div>`;
    });

    html += `
</body>
</html>`;

    return html;
  }

  escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  /**
   * Format price consistently with .00 format
   * @param {number|string} price - Price value
   * @returns {string} Formatted price string
   */
  formatPrice(price) {
    if (typeof price === 'number') {
      return `$${price.toFixed(2)}`;
    }
    if (typeof price === 'string') {
      const num = parseFloat(price.replace(/[^0-9.]/g, ''));
      return isNaN(num) ? '$0.00' : `$${num.toFixed(2)}`;
    }
    return '$0.00';
  }

  /**
   * Format shipping information
   * @param {object} product - Product data
   * @returns {string} Formatted shipping info
   */
  formatShippingInfo(product) {
    if (product.shipping === 0) {
      return '<span style="color: #27ae60;">Free shipping</span>';
    }
    if (product.shipping > 0) {
      return `<span style="color: #e67e22;">+${this.formatPrice(product.shipping)} shipping</span>`;
    }
    return '<span style="color: #95a5a6;">Shipping: Unknown</span>';
  }

  /**
   * Calculate total cost (price + shipping)
   * @param {object} product - Product data
   * @returns {number} Total cost
   */
  calculateTotalCost(product) {
    const price = typeof product.price === 'number' ? product.price : parseFloat(product.price || 0);
    const shipping = typeof product.shipping === 'number' ? product.shipping : parseFloat(product.shipping || 0);
    return price + shipping;
  }




  getConfiguration() {
    const bypassFilters = document.getElementById('bypassFilters').checked;

    return {
      // Search parameters
      minSales: parseInt(document.getElementById('minSales').value),
      daysBack: parseInt(document.getElementById('daysBack').value),
      maxPages: parseInt(document.getElementById('maxPages').value),

      // Profit filters
      minProfit: parseFloat(document.getElementById('minProfit').value),
      minMargin: parseFloat(document.getElementById('minMargin').value),
      maxRisk: parseInt(document.getElementById('maxRisk').value),

      // Price range
      priceRange: {
        min: parseInt(document.getElementById('minPrice').value),
        max: parseInt(document.getElementById('maxPrice').value)
      },

      // Bypass filters flag
      bypassFilters: bypassFilters,

      // Advanced options
      maxMatches: 2,
      minDemand: bypassFilters ? 0 : 30,
      minGrade: bypassFilters ? 'F' : 'C',

      profitOptions: {
        category: 'default',
        region: 'US',
        shippingType: 'standard',
        isInternational: false,
        usePromotedListings: false
      }
    };
  }

  validateConfiguration(config) {
    if (config.priceRange.min >= config.priceRange.max) {
      this.showNotification('❌ Min price must be less than max price', 'error');
      return false;
    }

    if (config.minSales < 0) {
      this.showNotification('❌ Min sales cannot be negative', 'error');
      return false;
    }

    if (config.maxPages < 1) {
      this.showNotification('❌ Max pages must be at least 1', 'error');
      return false;
    }

    return true;
  }

  displayResults() {
    const resultsContainer = document.getElementById('resultsList');
    const countElement = document.getElementById('opportunityCount');

    // Check if this is scraping-only mode (no profit analysis)
    const isScrapingOnly = this.results && this.results.length > 0 && !this.results[0].profitAnalysis;

    if (isScrapingOnly) {
      // Display scraped products without filtering/sorting
      countElement.textContent = this.results.length;

      if (this.results.length === 0) {
        resultsContainer.innerHTML = `
          <div class="no-results">
            <p>🔍 No products scraped yet.</p>
            <p>Click "Start Analysis" to begin scraping eBay products.</p>
          </div>
        `;
        return;
      }

      resultsContainer.innerHTML = '';

      this.results.slice(0, 50).forEach((product, index) => {
        const productElement = this.createScrapedProductElement(product, index + 1);
        resultsContainer.appendChild(productElement);
      });
    } else {
      // Original profit opportunity display logic
      let filteredResults = this.filterResults(this.results);
      filteredResults = this.sortResults(filteredResults);

      countElement.textContent = filteredResults.length;

      if (filteredResults.length === 0) {
        resultsContainer.innerHTML = `
          <div class="no-results">
            <p>🔍 No opportunities match your current filters.</p>
            <p>Try adjusting your criteria or running a new analysis.</p>
          </div>
        `;
        return;
      }

      // Show filter controls
      document.getElementById('filterControls').style.display = 'flex';

      resultsContainer.innerHTML = '';

      filteredResults.slice(0, 50).forEach((opportunity, index) => {
        const opportunityElement = this.createOpportunityElement(opportunity, index + 1);
        resultsContainer.appendChild(opportunityElement);
      });
    }
  }

  filterResults(results) {
    if (this.currentGradeFilter === 'all') return results;
    
    const gradeOrder = ['A+', 'A', 'B+', 'B', 'C+', 'C', 'D'];
    const filterIndex = gradeOrder.indexOf(this.currentGradeFilter);
    
    return results.filter(result => {
      const gradeIndex = gradeOrder.indexOf(result.profitAnalysis.opportunityGrade);
      return gradeIndex <= filterIndex;
    });
  }

  sortResults(results) {
    return [...results].sort((a, b) => {
      const analysisA = a.profitAnalysis;
      const analysisB = b.profitAnalysis;

      switch (this.currentSort) {
        case 'profit':
          return analysisB.grossProfit - analysisA.grossProfit;
        case 'margin':
          return analysisB.profitMargin - analysisA.profitMargin;
        case 'roi':
          return analysisB.roi - analysisA.roi;
        case 'grade':
          const gradeOrder = ['A+', 'A', 'B+', 'B', 'C+', 'C', 'D'];
          return gradeOrder.indexOf(analysisA.opportunityGrade) - gradeOrder.indexOf(analysisB.opportunityGrade);
        default:
          return analysisB.grossProfit - analysisA.grossProfit;
      }
    });
  }

  createScrapedProductElement(product, rank) {
    const div = document.createElement('div');
    div.className = 'scraped-product';

    const totalCost = this.calculateTotalCost(product);

    div.innerHTML = `
      <div class="product-header">
        <span class="product-rank">#${rank}</span>
        <span class="product-source">${product.source || 'eBay'}</span>
        ${product.condition ? `<span class="product-condition" style="background: #f39c12; color: white; padding: 2px 6px; border-radius: 3px; font-size: 11px;">${product.condition}</span>` : ''}
        ${product.listingType ? `<span class="listing-type" style="background: ${product.listingType === 'Auction' ? '#e74c3c' : '#27ae60'}; color: white; padding: 2px 6px; border-radius: 3px; font-size: 11px; margin-left: 4px;">${product.listingType}</span>` : ''}
      </div>
      ${product.imageUrl ? `
        <div class="product-image">
          <img src="${product.imageUrl}" alt="Product Image" style="max-width: 80px; max-height: 80px; object-fit: cover; border-radius: 4px;">
        </div>
      ` : ''}
      <div class="product-title">
        ${product.title || 'Unknown Product'}
      </div>
      <div class="product-price">
        <div style="font-size: 16px; font-weight: bold; color: #2c3e50;">
          Price: ${this.formatPrice(product.price)}
        </div>
        <div style="font-size: 14px; margin-top: 2px;">
          ${this.formatShippingInfo(product)}
        </div>
        <div style="font-size: 15px; font-weight: bold; color: #e74c3c; margin-top: 4px; border-top: 1px solid #ecf0f1; padding-top: 4px;">
          Total: ${this.formatPrice(totalCost)}
        </div>
      </div>

      <div class="product-details" style="margin-top: 8px; font-size: 12px; color: #7f8c8d;">
        ${product.seller ? `
          <div style="margin-bottom: 3px;">
            👤 Seller: <a href="https://www.ebay.com/usr/${product.seller}" target="_blank" style="color: #3498db; text-decoration: none;">${product.seller}</a>
            ${product.sellerFeedback ? `<span style="color: #27ae60; font-weight: bold;">(${product.sellerFeedback})</span>` : ''}
          </div>
        ` : ''}
        ${product.bids ? `<div style="margin-bottom: 3px;">🔨 ${product.bids}</div>` : ''}
        ${product.location ? `<div style="margin-bottom: 3px;">📍 Location: ${product.location}</div>` : ''}
        ${product.soldDate ? `<div style="margin-bottom: 3px;">📅 Sold: ${product.soldDate}</div>` : ''}
        ${product.badges && product.badges.length > 0 ? `<div style="margin-bottom: 3px;">🏷️ ${product.badges.join(', ')}</div>` : ''}
      </div>

      ${product.link ? `
        <div class="product-link">
          <a href="${product.link}" target="_blank">🔗 View on eBay</a>
        </div>
      ` : ''}
      <div class="product-meta">
        ${product.itemId ? `Item ID: ${product.itemId} | ` : ''}
        Scraped: ${new Date(product.timestamp || Date.now()).toLocaleTimeString()}
      </div>
    `;

    return div;
  }

  createOpportunityElement(opportunity, rank) {
    const div = document.createElement('div');
    div.className = 'opportunity';

    const profit = opportunity.profitAnalysis;
    const ebay = opportunity.ebayProduct;
    const amazon = opportunity.amazonProduct;
    const grade = profit.opportunityGrade;

    div.innerHTML = `
      <div class="opportunity-header">
        <span class="opportunity-rank">#${rank}</span>
        <span class="opportunity-grade grade-${grade.toLowerCase().replace('+', '-plus')}">${grade}</span>
      </div>
      <div class="opportunity-profit">
        $${profit.grossProfit.toFixed(2)} profit (${profit.profitMargin.toFixed(1)}% margin)
      </div>
      <div class="opportunity-titles">
        <div class="opportunity-title"><strong>eBay:</strong> ${ebay.title.substring(0, 55)}...</div>
        <div class="opportunity-title"><strong>Amazon:</strong> ${amazon.title.substring(0, 55)}...</div>
      </div>
      <div class="opportunity-metrics">
        <span>eBay: ${this.formatPrice(ebay.price)}</span>
        <span>Amazon: ${this.formatPrice(amazon.price)}</span>
        <span>ROI: ${profit.roi.toFixed(1)}%</span>
        <span>Sales: ${ebay.soldQuantity || 0}</span>
      </div>
      ${ebay.shipping !== undefined && ebay.shipping !== null ? `
        <div class="opportunity-shipping" style="font-size: 12px; color: #7f8c8d; margin-top: 4px;">
          eBay shipping: ${ebay.shipping === 0 ? 'Free' : this.formatPrice(ebay.shipping)} |
          Total eBay cost: ${this.formatPrice(this.calculateTotalCost(ebay))}
        </div>
      ` : ''}
    `;

    div.addEventListener('click', () => {
      this.showDetailedView(opportunity);
    });

    return div;
  }

  displaySummaryStats(summary) {
    if (!summary) return;

    document.getElementById('summaryStats').style.display = 'grid';
    document.getElementById('totalProfit').textContent = `$${summary.totalPotentialProfit?.toFixed(2) || '0.00'}`;
    document.getElementById('avgMargin').textContent = `${summary.averageMargin?.toFixed(1) || '0.0'}%`;
    document.getElementById('highValueCount').textContent = summary.highValueCount || 0;
  }

  showDetailedView(opportunity) {
    const profit = opportunity.profitAnalysis;
    const ebay = opportunity.ebayProduct;
    const amazon = opportunity.amazonProduct;

    const detailHTML = `
      <html>
      <head>
        <title>Arbitrage Opportunity Details</title>
        <style>
          body { font-family: Arial, sans-serif; padding: 20px; line-height: 1.6; }
          .header { background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
          .section { margin-bottom: 30px; }
          .product-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 30px; }
          .product-card { border: 1px solid #ddd; padding: 15px; border-radius: 8px; }
          .profit-table { width: 100%; border-collapse: collapse; }
          .profit-table th, .profit-table td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
          .profit-table th { background: #f8f9fa; }
          .total-row { font-weight: bold; border-top: 2px solid #333; }
          .grade-${profit.opportunityGrade.toLowerCase().replace('+', '-plus')} {
            background: #28a745; color: white; padding: 4px 8px; border-radius: 4px;
          }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>🎯 Arbitrage Opportunity Analysis</h1>
          <p><span class="grade-${profit.opportunityGrade.toLowerCase().replace('+', '-plus')}">${profit.opportunityGrade}</span>
             - $${profit.grossProfit.toFixed(2)} profit (${profit.profitMargin.toFixed(1)}% margin)</p>
        </div>

        <div class="product-grid">
          <div class="product-card">
            <h3>📦 eBay Product</h3>
            <p><strong>Title:</strong> ${ebay.title}</p>
            <p><strong>Price:</strong> ${this.formatPrice(ebay.price)}</p>
            ${ebay.shipping !== undefined && ebay.shipping !== null ? `
              <p><strong>Shipping:</strong> ${ebay.shipping === 0 ? 'Free' : this.formatPrice(ebay.shipping)}</p>
              <p><strong>Total Cost:</strong> <span style="color: #e74c3c; font-weight: bold;">${this.formatPrice(this.calculateTotalCost(ebay))}</span></p>
            ` : ''}
            <p><strong>Condition:</strong> ${ebay.condition || 'Not specified'}</p>
            <p><strong>Listing Type:</strong> <span style="color: ${ebay.listingType === 'Auction' ? '#e74c3c' : '#27ae60'}; font-weight: bold;">${ebay.listingType || 'Buy It Now'}</span></p>
            ${ebay.bids ? `<p><strong>Bidding:</strong> ${ebay.bids}</p>` : ''}
            ${ebay.seller ? `
              <p><strong>Seller:</strong> <a href="https://www.ebay.com/usr/${ebay.seller}" target="_blank" style="color: #3498db;">${ebay.seller}</a>
              ${ebay.sellerFeedback ? ` (${ebay.sellerFeedback} feedback)` : ''}</p>
            ` : ''}
            ${ebay.location ? `<p><strong>Location:</strong> ${ebay.location}</p>` : ''}
            <p><strong>Sales:</strong> ${ebay.soldQuantity || 0} in last 30 days</p>
            <p><a href="${ebay.link}" target="_blank">🔗 View on eBay</a></p>
          </div>

          <div class="product-card">
            <h3>📦 Amazon Product</h3>
            <p><strong>Title:</strong> ${amazon.title}</p>
            <p><strong>Price:</strong> ${this.formatPrice(amazon.price)}</p>
            <p><strong>Rating:</strong> ${amazon.rating || 'N/A'}/5 (${amazon.reviewCount || 0} reviews)</p>
            <p><strong>Prime:</strong> ${amazon.hasPrime ? 'Yes' : 'No'}</p>
            <p><a href="${amazon.link}" target="_blank">🔗 View on Amazon</a></p>
          </div>
        </div>

        <div class="section">
          <h3>💰 Detailed Profit Analysis</h3>
          <table class="profit-table">
            <tr><th>Item</th><th>Amount</th></tr>
            <tr><td>eBay Sale Price</td><td>${this.formatPrice(profit.revenue)}</td></tr>
            <tr><td>Amazon Cost (with tax)</td><td>-${this.formatPrice(profit.costs.amazon)}</td></tr>
            ${ebay.shipping !== undefined && ebay.shipping !== null && ebay.shipping > 0 ? `
              <tr><td>eBay Shipping Cost</td><td>-${this.formatPrice(ebay.shipping)}</td></tr>
            ` : ''}
            <tr><td>eBay Final Value Fee</td><td>-${this.formatPrice(profit.feeBreakdown.finalValue || 0)}</td></tr>
            <tr><td>Payment Processing Fee</td><td>-${this.formatPrice(profit.feeBreakdown.payment || 0)}</td></tr>
            <tr><td>Other Shipping Costs</td><td>-${this.formatPrice(profit.costs.shipping)}</td></tr>
            <tr class="total-row"><td><strong>Net Profit</strong></td><td><strong>${this.formatPrice(profit.grossProfit)}</strong></td></tr>
          </table>
        </div>

        <div class="section">
          <h3>📊 Key Metrics</h3>
          <p><strong>Profit Margin:</strong> ${profit.profitMargin.toFixed(1)}%</p>
          <p><strong>Return on Investment:</strong> ${profit.roi.toFixed(1)}%</p>
          <p><strong>Risk Score:</strong> ${profit.riskScore}/100</p>
          <p><strong>Demand Score:</strong> ${profit.demandScore}/100</p>
          <p><strong>Product Similarity:</strong> ${(amazon.similarity * 100).toFixed(1)}%</p>
        </div>
      </body>
      </html>
    `;

    const detailWindow = window.open('', '_blank', 'width=900,height=700,scrollbars=yes');
    detailWindow.document.write(detailHTML);
    detailWindow.document.close();
  }

  async exportToCSV() {
    if (this.results.length === 0) {
      this.showNotification('❌ No results to export', 'error');
      return;
    }

    const csvContent = this.generateCSV();
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);

    const a = document.createElement('a');
    a.href = url;
    a.download = `arbitrage_opportunities_${new Date().toISOString().split('T')[0]}.csv`;
    a.click();

    URL.revokeObjectURL(url);
    this.showNotification('📊 Results exported to CSV', 'success');
  }

  generateCSV() {
    const headers = [
      'Rank', 'Grade', 'eBay Title', 'Amazon Title', 'eBay Price', 'eBay Shipping', 'eBay Total Cost', 'Amazon Price',
      'Gross Profit', 'Profit Margin %', 'ROI %', 'Sales Count', 'Risk Score', 'Demand Score', 'Similarity %',
      'Condition', 'Listing Type', 'Seller', 'Seller Feedback', 'Location', 'Bids', 'eBay Link', 'Amazon Link'
    ];

    const rows = this.results.map((opportunity, index) => {
      const profit = opportunity.profitAnalysis;
      const ebay = opportunity.ebayProduct;
      const amazon = opportunity.amazonProduct;

      return [
        index + 1,
        profit.opportunityGrade,
        `"${ebay.title.replace(/"/g, '""')}"`,
        `"${amazon.title.replace(/"/g, '""')}"`,
        (ebay.price || 0).toFixed(2),
        (ebay.shipping || 0).toFixed(2),
        this.calculateTotalCost(ebay).toFixed(2),
        (amazon.price || 0).toFixed(2),
        (profit.grossProfit || 0).toFixed(2),
        (profit.profitMargin || 0).toFixed(1),
        (profit.roi || 0).toFixed(1),
        ebay.soldQuantity || 0,
        profit.riskScore || 0,
        profit.demandScore || 0,
        ((amazon.similarity || 0) * 100).toFixed(1),
        `"${(ebay.condition || '').replace(/"/g, '""')}"`,
        `"${(ebay.listingType || '').replace(/"/g, '""')}"`,
        `"${(ebay.seller || '').replace(/"/g, '""')}"`,
        `"${(ebay.sellerFeedback || '').replace(/"/g, '""')}"`,
        `"${(ebay.location || '').replace(/"/g, '""')}"`,
        `"${(ebay.bids || '').replace(/"/g, '""')}"`,
        ebay.link || '',
        amazon.link || ''
      ].join(',');
    });

    return [headers.join(','), ...rows].join('\n');
  }

  async loadPreviousResults() {
    try {
      const response = await this.sendMessage({ action: 'getResults' });
      if (response.success && response.results.length > 0) {
        this.results = response.results;
        this.displayResults();
        document.getElementById('exportResults').disabled = false;
        this.showNotification('📋 Previous results loaded', 'info');
      }

      if (response.isProcessing) {
        this.setProcessingState(true);
        this.showProgressSection();
      }
    } catch (error) {
      console.warn('Could not load previous results:', error);
    }
  }

  startProgressPolling() {
    // Listen for progress updates from background script
    chrome.runtime.onMessage.addListener((message) => {
      if (message.type === 'PROGRESS_UPDATE') {
        this.updateProgress(message.progress.current, message.progress.stage);
      }
    });
  }

  setProcessingState(processing) {
    this.isProcessing = processing;
    const startBtn = document.getElementById('startAnalysis');
    const stopBtn = document.getElementById('stopAnalysis');

    if (processing) {
      // Hide start button, show stop button
      startBtn.style.display = 'none';
      stopBtn.style.display = 'block';
      this.updateStatusIndicator('processing', 'Analysis in progress...');
    } else {
      // Show start button, hide stop button
      startBtn.style.display = 'block';
      stopBtn.style.display = 'none';
      startBtn.textContent = '🚀 Start Analysis';
      this.updateStatusIndicator('ready', 'Ready to start');
    }
  }

  updateStatusIndicator(state, text) {
    const statusText = document.querySelector('.status-text');
    const statusDot = document.querySelector('.status-dot');

    statusText.textContent = text;

    // Remove all state classes
    statusDot.classList.remove('ready', 'processing', 'error');
    // Add current state class
    statusDot.classList.add(state);
  }

  showProgressSection() {
    document.getElementById('progressSection').style.display = 'block';
  }

  updateProgress(percent, message) {
    document.getElementById('progressPercent').textContent = `${percent}%`;
    document.getElementById('progressFill').style.width = `${percent}%`;
    document.getElementById('progressStatus').textContent = message;

    if (percent === 100 || percent === 0) {
      setTimeout(() => {
        if (!this.isProcessing) {
          document.getElementById('progressSection').style.display = 'none';
        }
      }, 3000);
    }
  }

  showNotification(message, type) {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 12px 16px;
      border-radius: 6px;
      color: white;
      font-weight: 500;
      z-index: 1000;
      max-width: 300px;
      word-wrap: break-word;
      background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#17a2b8'};
    `;

    document.body.appendChild(notification);

    // Remove after 4 seconds
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 4000);
  }

  showHelp() {
    const helpContent = `
      <h2>🎯 eBay Arbitrage Researcher Pro - Help</h2>
      <h3>How it works:</h3>
      <ol>
        <li><strong>Configure Settings:</strong> Set minimum sales, profit margins, and price ranges</li>
        <li><strong>Start Analysis:</strong> Click "Start Arbitrage Analysis" to begin scanning</li>
        <li><strong>Review Results:</strong> Browse profitable opportunities sorted by potential</li>
        <li><strong>Export Data:</strong> Download CSV for purchasing decisions</li>
      </ol>

      <h3>Key Metrics:</h3>
      <ul>
        <li><strong>Grade:</strong> A+ (best) to D (worst) opportunity rating</li>
        <li><strong>Profit Margin:</strong> Percentage profit after all fees</li>
        <li><strong>ROI:</strong> Return on investment percentage</li>
        <li><strong>Risk Score:</strong> 0 (low risk) to 100 (high risk)</li>
      </ul>

      <h3>Tips:</h3>
      <ul>
        <li>Start with 3-5 minimum sales for reliable demand signals</li>
        <li>Focus on A and B grade opportunities</li>
        <li>Consider shipping costs and handling time</li>
        <li>Verify product condition and authenticity</li>
      </ul>
    `;

    this.showModal('Help', helpContent);
  }

  showSettings() {
    const settingsContent = `
      <h2>⚙️ Advanced Settings</h2>
      <p>Advanced configuration options will be available in future updates.</p>
      <p>Current features:</p>
      <ul>
        <li>Automatic fee calculation</li>
        <li>Multi-marketplace support</li>
        <li>Risk assessment</li>
        <li>Demand analysis</li>
      </ul>
    `;

    this.showModal('Settings', settingsContent);
  }

  showAbout() {
    const aboutContent = `
      <h2>ℹ️ About eBay Arbitrage Researcher Pro</h2>
      <p><strong>Version:</strong> 1.0.0</p>
      <p><strong>Purpose:</strong> Professional arbitrage opportunity finder</p>

      <h3>Features:</h3>
      <ul>
        <li>🔍 Automated eBay sold listings analysis</li>
        <li>🎯 Amazon product matching</li>
        <li>💰 Comprehensive profit calculations</li>
        <li>📊 Risk and demand assessment</li>
        <li>📈 Opportunity grading system</li>
        <li>📋 CSV export functionality</li>
      </ul>

      <p><strong>Built for:</strong> Drop shipping and retail arbitrage professionals</p>
      <p><strong>License:</strong> Free for personal use</p>
    `;

    this.showModal('About', aboutContent);
  }

  showModal(title, content) {
    const modal = window.open('', '_blank', 'width=600,height=500,scrollbars=yes');
    modal.document.write(`
      <html>
      <head>
        <title>${title}</title>
        <style>
          body { font-family: Arial, sans-serif; padding: 20px; line-height: 1.6; }
          h2 { color: #333; border-bottom: 2px solid #667eea; padding-bottom: 10px; }
          h3 { color: #495057; margin-top: 20px; }
          ul, ol { margin-left: 20px; }
          li { margin-bottom: 5px; }
        </style>
      </head>
      <body>${content}</body>
      </html>
    `);
    modal.document.close();
  }

  sendMessage(message) {
    console.log('🚀 POPUP SENDING MESSAGE:', JSON.stringify(message));
    return new Promise((resolve, reject) => {
      chrome.runtime.sendMessage(message, (response) => {
        console.log('📥 POPUP RECEIVED RESPONSE:', response);
        console.log('🔍 Chrome runtime error:', chrome.runtime.lastError);

        if (chrome.runtime.lastError) {
          console.error('❌ Message sending error:', chrome.runtime.lastError.message);
          reject(new Error(chrome.runtime.lastError.message));
        } else if (!response) {
          console.error('❌ No response received from service worker');
          reject(new Error('No response from service worker'));
        } else {
          console.log('✅ Message sent successfully, response:', response);
          resolve(response);
        }
      });
    });
  }
}

// Initialize UI when popup loads
document.addEventListener('DOMContentLoaded', () => {
  new ArbitrageUI();
});
