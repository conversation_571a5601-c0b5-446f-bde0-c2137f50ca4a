/**
 * eBay Scraper Pro - Simplified version for content script compatibility
 */
class EbayScraperPro {
  constructor() {
    this.isActive = false;
    this.results = [];
    console.log('🔍 EbayScraperPro initialized');
  }

  /**
   * Start bulk scraping operation
   * @param {object} config - Scraping configuration
   * @returns {Promise<object>} Scraping results
   */
  async startBulkScraping(config = {}) {
    try {
      this.isActive = true;
      console.log('🚀 Starting bulk scraping with config:', config);

      const {
        maxItems = 50,
        delay = 1000,
        selectors = this.getDefaultSelectors()
      } = config;

      // Find all listing items on the page
      const listingElements = document.querySelectorAll(selectors.listingItem);
      console.log(`📋 Found ${listingElements.length} listing elements`);

      const results = [];
      const maxToProcess = Math.min(listingElements.length, maxItems);

      for (let i = 0; i < maxToProcess; i++) {
        try {
          const listing = await this.scrapeListing(listingElements[i], selectors);
          if (listing) {
            results.push(listing);
          }

          // Add delay between items
          if (i < maxToProcess - 1) {
            await this.delay(delay);
          }
        } catch (error) {
          console.error(`❌ Error scraping listing ${i}:`, error);
        }
      }

      this.results = results;
      this.isActive = false;

      console.log(`✅ Bulk scraping completed: ${results.length} items scraped`);
      return {
        success: true,
        itemCount: results.length,
        items: results,
        timestamp: Date.now()
      };

    } catch (error) {
      this.isActive = false;
      console.error('❌ Bulk scraping failed:', error);
      return {
        success: false,
        error: error.message,
        itemCount: 0,
        items: []
      };
    }
  }

  /**
   * Scrape individual listing with superhuman data extraction
   * @param {Element} element - Listing DOM element
   * @param {object} selectors - CSS selectors
   * @returns {object} Scraped listing data
   */
  async scrapeListing(element, selectors) {
    try {
      // ✅ DEBUG: Log what we're actually extracting
      console.log('🔍 DEBUGGING ELEMENT EXTRACTION:');
      console.log('🔍 Element HTML:', element.outerHTML.substring(0, 500));
      console.log('🔍 Element classes:', element.className);

      const titleElement = element.querySelector(selectors.title);
      const priceElement = element.querySelector(selectors.price);

      console.log('🔍 Title element found:', !!titleElement);
      console.log('🔍 Title element HTML:', titleElement?.outerHTML.substring(0, 200));
      console.log('🔍 Title text:', titleElement?.textContent?.trim());

      console.log('🔍 Price element found:', !!priceElement);
      console.log('🔍 Price element HTML:', priceElement?.outerHTML.substring(0, 200));
      console.log('🔍 Price text:', priceElement?.textContent?.trim());

      const listing = {
        // Basic data
        title: this.extractText(element, selectors.title),
        price: this.extractPrice(element, selectors.price),
        shipping: this.extractText(element, selectors.shipping),
        condition: this.extractText(element, selectors.condition),
        seller: this.extractText(element, selectors.seller),
        url: this.extractUrl(element, selectors.url),
        image: this.extractImage(element, selectors.image),
        timestamp: Date.now(),

        // 🚀 SUPERHUMAN ENHANCEMENTS - Product Identifiers (Gold for matching!)
        upc: this.extractProductIdentifier(element, 'UPC'),
        ean: this.extractProductIdentifier(element, 'EAN'),
        isbn: this.extractProductIdentifier(element, 'ISBN'),
        gtin: this.extractProductIdentifier(element, 'GTIN'),
        mpn: this.extractProductIdentifier(element, 'MPN'),

        // eBay-specific identifiers
        ebayItemId: this.extractEbayItemId(element),
        epid: this.extractProductIdentifier(element, 'ePID'),

        // Enhanced metadata for precision matching
        brand: this.extractBrand(element),
        model: this.extractModel(element),
        color: this.extractColor(element),
        size: this.extractSize(element),

        // Sold history and demand metrics
        soldHistory: this.extractSoldHistory(element),
        watchCount: this.extractWatchCount(element),
        bidCount: this.extractBidCount(element),

        // Additional metadata
        location: this.extractLocation(element),
        shippingCost: this.extractShippingCost(element),
        returnPolicy: this.extractReturnPolicy(element),

        // Confidence scoring for match validation
        extractionConfidence: 0
      };

      // Clean up the data
      listing.price = this.parsePrice(listing.price);
      listing.shipping = this.parsePrice(listing.shipping);
      listing.shippingCost = this.parsePrice(listing.shippingCost);

      // Calculate extraction confidence score
      listing.extractionConfidence = this.calculateExtractionConfidence(listing);

      return listing;
    } catch (error) {
      console.error('❌ Error scraping individual listing:', error);
      return null;
    }
  }

  /**
   * 🚀 WORLD-CLASS CSS SELECTORS for eBay with comprehensive extraction
   * @returns {object} Enhanced selector configuration
   */
  getDefaultSelectors() {
    return {
      // 🎯 CORE SELECTORS (World-class enhanced)
      listingItem: '.s-item',
      title: '.s-item__title',
      price: '.s-item__price',
      shipping: '.s-item__shipping, .s-item__logisticsCost',
      condition: '.SECONDARY_INFO',
      seller: '.s-item__seller-info-text',
      url: '.s-item__info a.s-item__link',
      image: '.s-item__image-img',

      // 🆕 WORLD-CLASS ADDITIONS
      sellerFeedback: '.s-item__seller-info-feedback',
      listingFormat: '.s-item__purchase-options-with-icon',
      bids: 'span.s-item__bids',
      location: '.s-item__location',
      soldDate: '.s-item__title--tagblock .POSITIVE, .s-item__ended-date',
      bestOffer: '.s-item__title--tagblock .NEGATIVE',
      freeReturns: '.s-item__free-returns, .s-item__returns-accepted',
      category: '.s-item__subtitle',
      charity: '[aria-label*="charity"], .s-item__benefit-charity',

      // 🔧 ENHANCED SELECTORS for superhuman extraction
      itemSpecs: '.s-item__detail, .s-item__details, .u-flL',
      soldHistory: '.s-item__sold, .NEGATIVE',
      watchCount: '.s-item__watchcount',
      bidCount: '.s-item__bids',
      returnPolicy: '.s-item__returns',

      // 🛡️ MULTI-SELECTOR FALLBACKS for robust extraction
      titleFallbacks: ['.s-item__title', '.it-ttl', '.vip'],
      priceFallbacks: ['.s-item__price', '.notranslate', '.u-flL'],
      conditionFallbacks: ['.SECONDARY_INFO', '.s-item__subtitle', '.condText'],
      urlFallbacks: ['.s-item__info a.s-item__link', '.s-item__link', 'a.s-item__link'],
      imageFallbacks: ['.s-item__image-img', '.s-item__image img', '.s-item__wrapper img']
    };
  }

  /**
   * Extract text from element using selector
   * @param {Element} parent - Parent element
   * @param {string} selector - CSS selector
   * @returns {string} Extracted text
   */
  extractText(parent, selector) {
    try {
      const element = parent.querySelector(selector);
      return element ? element.textContent.trim() : '';
    } catch (error) {
      return '';
    }
  }

  /**
   * Extract price text from element
   * @param {Element} parent - Parent element
   * @param {string} selector - CSS selector
   * @returns {string} Price text
   */
  extractPrice(parent, selector) {
    try {
      const element = parent.querySelector(selector);
      return element ? element.textContent.trim() : '0';
    } catch (error) {
      return '0';
    }
  }

  /**
   * Extract URL from element
   * @param {Element} parent - Parent element
   * @param {string} selector - CSS selector
   * @returns {string} URL
   */
  extractUrl(parent, selector) {
    try {
      const element = parent.querySelector(selector);
      return element ? element.href : '';
    } catch (error) {
      return '';
    }
  }

  /**
   * Extract image URL from element
   * @param {Element} parent - Parent element
   * @param {string} selector - CSS selector
   * @returns {string} Image URL
   */
  extractImage(parent, selector) {
    try {
      const element = parent.querySelector(selector);
      return element ? element.src : '';
    } catch (error) {
      return '';
    }
  }

  /**
   * Parse price string to number
   * @param {string} priceText - Price text
   * @returns {number} Parsed price
   */
  parsePrice(priceText) {
    try {
      const cleaned = priceText.replace(/[^0-9.]/g, '');
      return parseFloat(cleaned) || 0;
    } catch (error) {
      return 0;
    }
  }

  // 🚀 SUPERHUMAN EXTRACTION METHODS - The "Gold for Matching" Features

  /**
   * Extract product identifiers (UPC/EAN/ISBN/GTIN/MPN) - GOLD for matching!
   * @param {Element} element - Parent element
   * @param {string} identifierType - Type of identifier to extract
   * @returns {string} Product identifier
   */
  extractProductIdentifier(element, identifierType) {
    try {
      // Multiple strategies for finding product identifiers
      const strategies = [
        () => this.extractFromItemSpecs(element, identifierType),
        () => this.extractFromTitle(element, identifierType),
        () => this.extractFromDescription(element, identifierType),
        () => this.extractFromDataAttributes(element, identifierType),
        () => this.extractFromUrl(element, identifierType)
      ];

      for (const strategy of strategies) {
        const result = strategy();
        if (result && this.validateIdentifier(result, identifierType)) {
          console.log(`✅ Found ${identifierType}: ${result}`);
          return result;
        }
      }

      return '';
    } catch (error) {
      console.error(`❌ Error extracting ${identifierType}:`, error);
      return '';
    }
  }

  /**
   * Extract identifier from item specifications
   * @param {Element} element - Parent element
   * @param {string} identifierType - Type of identifier
   * @returns {string} Identifier value
   */
  extractFromItemSpecs(element, identifierType) {
    try {
      const specElements = element.querySelectorAll('.s-item__detail, .s-item__details, .u-flL');
      const patterns = this.getIdentifierPatterns(identifierType);

      for (const specElement of specElements) {
        const text = specElement.textContent;
        for (const pattern of patterns) {
          const match = text.match(pattern);
          if (match) {
            return match[1] || match[0];
          }
        }
      }
      return '';
    } catch (error) {
      return '';
    }
  }

  /**
   * Extract identifier from title using regex patterns
   * @param {Element} element - Parent element
   * @param {string} identifierType - Type of identifier
   * @returns {string} Identifier value
   */
  extractFromTitle(element, identifierType) {
    try {
      const title = this.extractText(element, '.s-item__title');
      const patterns = this.getIdentifierPatterns(identifierType);

      for (const pattern of patterns) {
        const match = title.match(pattern);
        if (match) {
          return match[1] || match[0];
        }
      }
      return '';
    } catch (error) {
      return '';
    }
  }

  /**
   * Get regex patterns for different identifier types
   * @param {string} identifierType - Type of identifier
   * @returns {Array} Array of regex patterns
   */
  getIdentifierPatterns(identifierType) {
    const patterns = {
      UPC: [
        /UPC[:\s]*([0-9]{12})/i,
        /Universal Product Code[:\s]*([0-9]{12})/i,
        /\b([0-9]{12})\b/
      ],
      EAN: [
        /EAN[:\s]*([0-9]{13})/i,
        /European Article Number[:\s]*([0-9]{13})/i,
        /\b([0-9]{13})\b/
      ],
      ISBN: [
        /ISBN[:\s]*([0-9-]{10,17})/i,
        /\b([0-9]{9}[0-9X])\b/,
        /\b([0-9]{13})\b/
      ],
      GTIN: [
        /GTIN[:\s]*([0-9]{8,14})/i,
        /Global Trade Item Number[:\s]*([0-9]{8,14})/i
      ],
      MPN: [
        /MPN[:\s]*([A-Z0-9-]+)/i,
        /Manufacturer Part Number[:\s]*([A-Z0-9-]+)/i,
        /Model[:\s]*([A-Z0-9-]+)/i
      ],
      ePID: [
        /ePID[:\s]*([0-9]+)/i,
        /Product ID[:\s]*([0-9]+)/i
      ]
    };

    return patterns[identifierType] || [];
  }

  /**
   * Extract from data attributes
   * @param {Element} element - Parent element
   * @param {string} identifierType - Type of identifier
   * @returns {string} Identifier value
   */
  extractFromDataAttributes(element, identifierType) {
    try {
      const attributes = [`data-${identifierType.toLowerCase()}`, `data-${identifierType}`];
      for (const attr of attributes) {
        const value = element.getAttribute(attr);
        if (value) return value;
      }
      return '';
    } catch (error) {
      return '';
    }
  }

  /**
   * Extract from URL parameters
   * @param {Element} element - Parent element
   * @param {string} identifierType - Type of identifier
   * @returns {string} Identifier value
   */
  extractFromUrl(element, identifierType) {
    try {
      const url = this.extractUrl(element, '.s-item__link');
      const urlParams = new URLSearchParams(url.split('?')[1] || '');
      return urlParams.get(identifierType.toLowerCase()) || '';
    } catch (error) {
      return '';
    }
  }

  /**
   * Extract eBay item ID from URL or data attributes
   * @param {Element} element - Parent element
   * @returns {string} eBay item ID
   */
  extractEbayItemId(element) {
    try {
      // Try data attributes first
      const itemId = element.getAttribute('data-itemid') ||
                    element.getAttribute('data-item-id') ||
                    element.getAttribute('data-id');
      if (itemId) return itemId;

      // Extract from URL
      const url = this.extractUrl(element, '.s-item__link');
      const match = url.match(/\/itm\/([0-9]+)/);
      return match ? match[1] : '';
    } catch (error) {
      return '';
    }
  }

  /**
   * Extract brand information
   * @param {Element} element - Parent element
   * @returns {string} Brand name
   */
  extractBrand(element) {
    try {
      const title = this.extractText(element, '.s-item__title');
      const commonBrands = ['Apple', 'Samsung', 'Sony', 'Nike', 'Adidas', 'Canon', 'Nikon', 'Dell', 'HP', 'Lenovo'];

      for (const brand of commonBrands) {
        if (title.toLowerCase().includes(brand.toLowerCase())) {
          return brand;
        }
      }

      // Extract first word if it looks like a brand
      const firstWord = title.split(' ')[0];
      if (firstWord && firstWord.length > 2 && /^[A-Z]/.test(firstWord)) {
        return firstWord;
      }

      return '';
    } catch (error) {
      return '';
    }
  }

  /**
   * Extract sold history and demand metrics
   * @param {Element} element - Parent element
   * @returns {object} Sold history data
   */
  extractSoldHistory(element) {
    try {
      const soldText = this.extractText(element, '.s-item__sold, .NEGATIVE');
      const soldMatch = soldText.match(/(\d+)\s*sold/i);
      const soldCount = soldMatch ? parseInt(soldMatch[1]) : 0;

      return {
        soldCount,
        soldText,
        lastSoldDate: this.extractLastSoldDate(element),
        demandScore: this.calculateDemandScore(soldCount)
      };
    } catch (error) {
      return { soldCount: 0, soldText: '', lastSoldDate: null, demandScore: 0 };
    }
  }

  /**
   * Calculate demand score based on sold count
   * @param {number} soldCount - Number of items sold
   * @returns {number} Demand score (0-100)
   */
  calculateDemandScore(soldCount) {
    if (soldCount >= 50) return 100;
    if (soldCount >= 20) return 80;
    if (soldCount >= 10) return 60;
    if (soldCount >= 5) return 40;
    if (soldCount >= 3) return 20;
    return soldCount > 0 ? 10 : 0;
  }

  /**
   * Validate identifier format
   * @param {string} identifier - Identifier to validate
   * @param {string} type - Type of identifier
   * @returns {boolean} Is valid
   */
  validateIdentifier(identifier, type) {
    const validationRules = {
      UPC: /^[0-9]{12}$/,
      EAN: /^[0-9]{13}$/,
      ISBN: /^[0-9-]{10,17}$/,
      GTIN: /^[0-9]{8,14}$/,
      MPN: /^[A-Z0-9-]{3,}$/i
    };

    const rule = validationRules[type];
    return rule ? rule.test(identifier) : identifier.length > 0;
  }

  /**
   * Calculate extraction confidence score
   * @param {object} listing - Listing data
   * @returns {number} Confidence score (0-100)
   */
  calculateExtractionConfidence(listing) {
    let score = 0;

    // Product identifiers are gold - high weight
    if (listing.upc) score += 30;
    if (listing.ean) score += 30;
    if (listing.isbn) score += 25;
    if (listing.gtin) score += 20;
    if (listing.mpn) score += 15;
    if (listing.ebayItemId) score += 10;

    // Basic data quality
    if (listing.title && listing.title.length > 10) score += 10;
    if (listing.price > 0) score += 5;
    if (listing.brand) score += 5;
    if (listing.soldHistory.soldCount > 0) score += 10;

    return Math.min(score, 100);
  }

  // Additional extraction methods for complete superhuman capabilities

  /**
   * Extract model information from title
   * @param {Element} element - Parent element
   * @returns {string} Model information
   */
  extractModel(element) {
    try {
      const title = this.extractText(element, '.s-item__title');
      // Look for model patterns like "iPhone 12", "Galaxy S21", etc.
      const modelMatch = title.match(/\b([A-Z0-9]+\s*[0-9]+[A-Z]*)\b/i);
      return modelMatch ? modelMatch[1] : '';
    } catch (error) {
      return '';
    }
  }

  /**
   * Extract color information
   * @param {Element} element - Parent element
   * @returns {string} Color
   */
  extractColor(element) {
    try {
      const title = this.extractText(element, '.s-item__title');
      const colors = ['Black', 'White', 'Red', 'Blue', 'Green', 'Yellow', 'Pink', 'Purple', 'Gray', 'Silver', 'Gold'];

      for (const color of colors) {
        if (title.toLowerCase().includes(color.toLowerCase())) {
          return color;
        }
      }
      return '';
    } catch (error) {
      return '';
    }
  }

  /**
   * Extract size information
   * @param {Element} element - Parent element
   * @returns {string} Size
   */
  extractSize(element) {
    try {
      const title = this.extractText(element, '.s-item__title');
      const sizeMatch = title.match(/\b(XS|S|M|L|XL|XXL|\d+["\s]*x\s*\d+["]?|\d+\s*(GB|TB|MB))\b/i);
      return sizeMatch ? sizeMatch[1] : '';
    } catch (error) {
      return '';
    }
  }

  /**
   * Extract watch count
   * @param {Element} element - Parent element
   * @returns {number} Watch count
   */
  extractWatchCount(element) {
    try {
      const watchText = this.extractText(element, '.s-item__watchcount');
      const match = watchText.match(/(\d+)/);
      return match ? parseInt(match[1]) : 0;
    } catch (error) {
      return 0;
    }
  }

  /**
   * Extract bid count
   * @param {Element} element - Parent element
   * @returns {number} Bid count
   */
  extractBidCount(element) {
    try {
      const bidText = this.extractText(element, '.s-item__bids');
      const match = bidText.match(/(\d+)/);
      return match ? parseInt(match[1]) : 0;
    } catch (error) {
      return 0;
    }
  }

  /**
   * Extract location information
   * @param {Element} element - Parent element
   * @returns {string} Location
   */
  extractLocation(element) {
    try {
      return this.extractText(element, '.s-item__location');
    } catch (error) {
      return '';
    }
  }

  /**
   * Extract shipping cost
   * @param {Element} element - Parent element
   * @returns {string} Shipping cost
   */
  extractShippingCost(element) {
    try {
      return this.extractText(element, '.s-item__shipping');
    } catch (error) {
      return '';
    }
  }

  /**
   * Extract return policy
   * @param {Element} element - Parent element
   * @returns {string} Return policy
   */
  extractReturnPolicy(element) {
    try {
      return this.extractText(element, '.s-item__returns');
    } catch (error) {
      return '';
    }
  }

  /**
   * Extract last sold date (if available)
   * @param {Element} element - Parent element
   * @returns {Date|null} Last sold date
   */
  extractLastSoldDate(element) {
    try {
      // This would need to be implemented based on eBay's specific date format
      // For now, return current date as placeholder
      return new Date();
    } catch (error) {
      return null;
    }
  }

  /**
   * Extract from description text (if available)
   * @param {Element} element - Parent element
   * @param {string} identifierType - Type of identifier
   * @returns {string} Identifier value
   */
  extractFromDescription(element, identifierType) {
    try {
      // Look for description elements
      const descElements = element.querySelectorAll('.s-item__subtitle, .s-item__detail');
      const patterns = this.getIdentifierPatterns(identifierType);

      for (const descElement of descElements) {
        const text = descElement.textContent;
        for (const pattern of patterns) {
          const match = text.match(pattern);
          if (match) {
            return match[1] || match[0];
          }
        }
      }
      return '';
    } catch (error) {
      return '';
    }
  }

  /**
   * Delay execution
   * @param {number} ms - Milliseconds to delay
   * @returns {Promise} Promise that resolves after delay
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get current scraping status with enhanced metrics
   * @returns {object} Status information
   */
  getStatus() {
    return {
      isActive: this.isActive,
      resultCount: this.results.length,
      lastUpdate: Date.now(),
      // Enhanced status metrics
      identifierExtractionRate: this.calculateIdentifierExtractionRate(),
      averageConfidenceScore: this.calculateAverageConfidenceScore(),
      superhuman: true // 🚀 We're now superhuman!
    };
  }

  /**
   * Calculate identifier extraction rate
   * @returns {number} Percentage of items with identifiers
   */
  calculateIdentifierExtractionRate() {
    if (this.results.length === 0) return 0;

    const itemsWithIdentifiers = this.results.filter(item =>
      item.upc || item.ean || item.isbn || item.gtin || item.mpn
    ).length;

    return Math.round((itemsWithIdentifiers / this.results.length) * 100);
  }

  /**
   * Calculate average confidence score
   * @returns {number} Average confidence score
   */
  calculateAverageConfidenceScore() {
    if (this.results.length === 0) return 0;

    const totalScore = this.results.reduce((sum, item) =>
      sum + (item.extractionConfidence || 0), 0
    );

    return Math.round(totalScore / this.results.length);
  }
}

// Initialize globalContext if it doesn't exist
if (typeof window.globalContext === 'undefined') {
  window.globalContext = {};
}

// Export to multiple window locations for compatibility
window.ebayScraperPro = new EbayScraperPro();
window.EbayScraperPro = EbayScraperPro;
window.globalContext.ebayScraperPro = window.ebayScraperPro;

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
  module.exports = EbayScraperPro;
}

console.log('✅ EbayScraperPro loaded and exported to window.ebayScraperPro');
