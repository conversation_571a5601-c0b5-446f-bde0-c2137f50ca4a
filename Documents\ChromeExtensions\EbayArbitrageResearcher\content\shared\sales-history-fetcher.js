/**
 * 🚀 SALES HISTORY FETCHER - Background sales data collection
 * 
 * Efficiently fetches sold listings data for active eBay listings
 * to determine sales frequency, dates, and demand metrics.
 */

class SalesHistoryFetcher {
  constructor() {
    this.cache = new Map(); // Cache results to avoid duplicate requests
    this.requestQueue = [];
    this.isProcessing = false;
    this.maxConcurrent = 3; // Limit concurrent requests
    this.requestDelay = 1000; // 1 second between requests
  }

  /**
   * Fetch sales history for a product
   * @param {Object} product - Product data with title and itemId
   * @returns {Promise<Object>} Sales history data
   */
  async fetchSalesHistory(product) {
    const cacheKey = this.getCacheKey(product);
    
    // Check cache first
    if (this.cache.has(cacheKey)) {
      console.log(`📋 Cache hit for: ${product.title?.substring(0, 50)}...`);
      return this.cache.get(cacheKey);
    }

    // Add to queue for processing
    return new Promise((resolve, reject) => {
      this.requestQueue.push({
        product,
        cacheKey,
        resolve,
        reject,
        timestamp: Date.now()
      });

      this.processQueue();
    });
  }

  /**
   * Process the request queue with rate limiting
   */
  async processQueue() {
    if (this.isProcessing || this.requestQueue.length === 0) return;

    this.isProcessing = true;
    console.log(`🚀 Processing sales history queue: ${this.requestQueue.length} items`);

    while (this.requestQueue.length > 0) {
      const batch = this.requestQueue.splice(0, this.maxConcurrent);
      
      // Process batch in parallel
      const promises = batch.map(request => this.processSingleRequest(request));
      await Promise.allSettled(promises);

      // Rate limiting delay
      if (this.requestQueue.length > 0) {
        await this.delay(this.requestDelay);
      }
    }

    this.isProcessing = false;
    console.log('✅ Sales history queue processing complete');
  }

  /**
   * Process a single sales history request
   */
  async processSingleRequest(request) {
    try {
      const { product, cacheKey, resolve } = request;
      console.log(`🔍 Fetching sales history for: ${product.title?.substring(0, 50)}...`);

      const salesData = await this.searchSoldListings(product);
      
      // Cache the result
      this.cache.set(cacheKey, salesData);
      
      resolve(salesData);
    } catch (error) {
      console.warn(`⚠️ Failed to fetch sales history for ${request.product.title}:`, error);
      request.reject(error);
    }
  }

  /**
   * Search eBay sold listings for a product
   * 🚀 NEW: Uses direct purchase history endpoint for lightning-fast results
   */
  async searchSoldListings(product) {
    try {
      // 🚀 PHASE 1: Try direct purchase history endpoint (FASTEST)
      if (product.itemId && product.itemId !== 'unknown') {
        console.log(`🚀 Trying direct purchase history for item ${product.itemId}`);
        const directData = await this.fetchDirectPurchaseHistory(product.itemId);
        if (directData) {
          console.log(`✅ Direct purchase history response received: ${directData.salesFrequency || 0} sales found`);
          return directData;
        } else {
          console.log(`📭 Direct purchase history failed for item ${product.itemId}`);
        }
      }

      // 🚀 PHASE 2: Return empty data with verification link (no more search fallback)
      console.log(`⚠️ FALLBACK: Direct purchase history failed for item ${product.itemId}`);
      console.log(`🚫 Skipping search-based fallback to avoid inaccurate data`);

      // Return minimal data with verification link - no more inflated search results
      console.log(`📭 No direct purchase history available for item ${product.itemId}`);
      return {
        salesFrequency: 0,
        salesHistory: [],
        lastSoldDate: null,
        averageSalePrice: null,
        salesVelocity: 0,
        demandScore: 0,
        isSoldListing: false,
        extractedFrom: 'direct-purchase-history-unavailable',
        itemId: product.itemId,
        purchaseHistoryUrl: `https://www.ebay.com/bin/purchaseHistory?item=${product.itemId}`,
        verificationNote: 'Direct purchase history not accessible - use verification link',
        timestamp: Date.now()
      };

    } catch (error) {
      console.warn('⚠️ Error searching sold listings:', error);
      return this.getEmptySalesData();
    }
  }

  /**
   * 🚀 ENHANCED: Fetch direct purchase history using eBay's backend endpoint
   * This is MUCH faster and more accurate than search-based approaches
   */
  async fetchDirectPurchaseHistory(itemId) {
    try {
      const purchaseHistoryUrl = `https://www.ebay.com/bin/purchaseHistory?item=${itemId}`;
      console.log(`🚀 AUTHENTICATED: Requesting background tab for purchase history: ${purchaseHistoryUrl}`);

      // 🎯 AUTHENTICATED APPROACH: Request background tab from service worker
      const salesData = await this.requestBackgroundTabExtraction(purchaseHistoryUrl, itemId);
      return salesData;

    } catch (error) {
      console.warn(`❌ Error with authenticated purchase history for ${itemId}:`, error);
      return null;
    }
  }

  /**
   * 🎯 AUTHENTICATED: Request background tab extraction from service worker
   */
  async requestBackgroundTabExtraction(url, itemId) {
    return new Promise((resolve, reject) => {
      console.log(`🔐 AUTHENTICATED: Requesting background tab for ${itemId}`);

      // Request background tab from service worker
      chrome.runtime.sendMessage({
        action: 'createBackgroundTab',
        url: url,
        itemId: itemId
      }, (response) => {
        if (chrome.runtime.lastError) {
          console.error(`❌ Background tab request failed: ${chrome.runtime.lastError.message}`);
          reject(new Error(chrome.runtime.lastError.message));
          return;
        }

        if (response && response.success) {
          console.log(`✅ AUTHENTICATED: Background tab request accepted for ${itemId}`);

          // Set up listener for extraction results
          const messageListener = (message, sender, sendResponse) => {
            console.log(`📨 AUTHENTICATED DEBUG: Received message:`, message);

            if (message.action === 'purchaseHistoryExtracted' && message.itemId === itemId) {
              console.log(`📊 AUTHENTICATED DEBUG: Received purchase history data for ${itemId}:`, message.data);

              chrome.runtime.onMessage.removeListener(messageListener);

              if (message.data && message.data.salesFrequency > 0) {
                console.log(`✅ AUTHENTICATED DEBUG: Resolving with sales data for ${itemId}`);
                resolve(message.data);
              } else {
                console.log(`📭 AUTHENTICATED DEBUG: No sales data for ${itemId}, resolving with null`);
                resolve(null);
              }
            } else {
              console.log(`🔍 AUTHENTICATED DEBUG: Message not for this request - action: ${message.action}, itemId: ${message.itemId}, expected: ${itemId}`);
            }
          };

          chrome.runtime.onMessage.addListener(messageListener);

          // Extended timeout for debugging
          setTimeout(() => {
            console.log(`⏰ AUTHENTICATED DEBUG: Timeout reached for ${itemId} after 30 seconds`);
            chrome.runtime.onMessage.removeListener(messageListener);
            reject(new Error('Background tab extraction timeout after 30 seconds'));
          }, 30000); // Extended to 30 seconds

        } else {
          reject(new Error('Background tab request rejected'));
        }
      });
    });
  }

  /**
   * Clean product title for eBay search
   */
  cleanTitleForSearch(title) {
    if (!title) return '';
    
    return title
      // Remove common eBay-specific terms
      .replace(/\b(new|used|refurbished|open box|for parts)\b/gi, '')
      // Remove condition indicators
      .replace(/\b(excellent|good|fair|poor|mint)\b/gi, '')
      // Remove size/color variations that might be too specific
      .replace(/\b(size|color|colour)\s*:?\s*\w+/gi, '')
      // Remove special characters and extra spaces
      .replace(/[^\w\s-]/g, ' ')
      .replace(/\s+/g, ' ')
      .trim()
      // Take first 60 characters for search
      .substring(0, 60);
  }

  /**
   * Build eBay sold listings search URL
   */
  buildSoldListingsUrl(searchTitle) {
    const baseUrl = 'https://www.ebay.com/sch/i.html';
    const params = new URLSearchParams({
      '_nkw': searchTitle,
      'LH_Sold': '1',
      'LH_Complete': '1',
      '_sop': '13', // Sort by newest first
      '_ipg': '60'  // 60 results per page
    });

    return `${baseUrl}?${params.toString()}`;
  }

  /**
   * 🚀 NEW: Parse JSON purchase history response
   * eBay sometimes returns JSON data with purchase information
   */
  parseJSONPurchaseHistory(jsonData, itemId) {
    try {
      console.log(`🔍 Parsing JSON purchase history for item ${itemId}`);

      // Extract purchase data from various possible JSON structures
      const purchases = jsonData.purchases || jsonData.items || jsonData.history || jsonData.sales || [];

      if (!Array.isArray(purchases) || purchases.length === 0) {
        console.log(`📭 No purchases found in JSON data for item ${itemId}`);
        return null;
      }

      const salesHistory = [];
      let totalQuantity = 0;

      purchases.forEach(purchase => {
        const date = this.parseDate(purchase.date || purchase.purchaseDate || purchase.soldDate || purchase.timestamp);
        const price = this.parsePrice(purchase.price || purchase.amount || purchase.finalPrice || purchase.cost);
        const quantity = parseInt(purchase.quantity || purchase.qty || 1);

        if (date) {
          salesHistory.push({
            date,
            price: price || 0,
            quantity,
            source: 'json-purchase-history'
          });
          totalQuantity += quantity;
        }
      });

      if (salesHistory.length === 0) {
        return null;
      }

      // Sort by date (newest first)
      salesHistory.sort((a, b) => new Date(b.date) - new Date(a.date));

      const lastSoldDate = salesHistory[0].date;
      const averageSalePrice = salesHistory.filter(s => s.price > 0).length > 0
        ? salesHistory.filter(s => s.price > 0).reduce((sum, s) => sum + s.price, 0) / salesHistory.filter(s => s.price > 0).length
        : null;

      console.log(`📊 JSON purchase history for item ${itemId}: ${totalQuantity} total quantity in ${salesHistory.length} transactions`);

      return {
        salesFrequency: totalQuantity,
        salesHistory: salesHistory.slice(0, 10),
        lastSoldDate,
        averageSalePrice,
        salesVelocity: this.calculateSalesVelocity(totalQuantity, salesHistory),
        demandScore: this.calculateDemandScore(totalQuantity),
        isSoldListing: false,
        extractedFrom: 'json-purchase-history',
        itemId: itemId,
        totalTransactions: salesHistory.length,
        timestamp: Date.now()
      };

    } catch (error) {
      console.warn(`⚠️ Error parsing JSON purchase history for item ${itemId}:`, error);
      return null;
    }
  }

  /**
   * Parse date from various formats
   */
  parseDate(dateInput) {
    if (!dateInput) return null;

    try {
      // Handle timestamp (milliseconds)
      if (typeof dateInput === 'number') {
        const date = new Date(dateInput);
        return date.toISOString().split('T')[0];
      }

      // Handle date string
      const date = new Date(dateInput);
      if (!isNaN(date.getTime())) {
        return date.toISOString().split('T')[0];
      }

      return null;
    } catch {
      return null;
    }
  }

  /**
   * Parse price from various formats
   */
  parsePrice(priceInput) {
    if (!priceInput) return null;

    try {
      if (typeof priceInput === 'number') {
        return priceInput;
      }

      if (typeof priceInput === 'string') {
        const price = parseFloat(priceInput.replace(/[^0-9.]/g, ''));
        return isNaN(price) ? null : price;
      }

      return null;
    } catch {
      return null;
    }
  }

  /**
   * 🎯 Search for exact item sales using targeted approach
   * This searches for the specific item ID and exact title matches
   */
  async searchExactItemSales(product) {
    try {
      console.log(`🎯 Searching for exact sales of item ${product.itemId}: ${product.title?.substring(0, 50)}...`);

      // Method 1: Search by exact item ID in sold listings
      if (product.itemId && product.itemId !== 'unknown') {
        const itemIdData = await this.searchByItemId(product.itemId);
        if (itemIdData && itemIdData.salesFrequency > 0) {
          console.log(`✅ Found sales data by item ID: ${itemIdData.salesFrequency} sales`);
          return itemIdData;
        }
      }

      // Method 2: Search by exact title match in sold listings
      const exactTitleData = await this.searchByExactTitle(product);
      if (exactTitleData && exactTitleData.salesFrequency > 0) {
        console.log(`✅ Found sales data by exact title: ${exactTitleData.salesFrequency} sales`);
        return exactTitleData;
      }

      console.log(`📭 No exact item sales found for ${product.itemId}`);
      return null;

    } catch (error) {
      console.warn(`⚠️ Error in exact item search for ${product.itemId}:`, error);
      return null;
    }
  }

  /**
   * 🎯 Search sold listings by exact item ID
   */
  async searchByItemId(itemId) {
    try {
      // Search for the exact item ID in sold listings
      const searchUrl = `https://www.ebay.com/sch/i.html?_nkw=${itemId}&LH_Sold=1&LH_Complete=1&_sop=13&_ipg=60`;
      console.log(`🔍 Searching by item ID: ${searchUrl}`);

      const response = await fetch(searchUrl);
      if (!response.ok) return null;

      const html = await response.text();
      return this.parseExactItemSales(html, itemId, 'item-id-search');

    } catch (error) {
      console.warn(`⚠️ Error searching by item ID ${itemId}:`, error);
      return null;
    }
  }

  /**
   * 🎯 Search sold listings by exact title match
   */
  async searchByExactTitle(product) {
    try {
      // Use the exact title (with quotes for exact match)
      const exactTitle = product.title.replace(/"/g, ''); // Remove existing quotes
      const searchUrl = `https://www.ebay.com/sch/i.html?_nkw="${encodeURIComponent(exactTitle)}"&LH_Sold=1&LH_Complete=1&_sop=13&_ipg=60`;
      console.log(`🔍 Searching by exact title: "${exactTitle.substring(0, 50)}..."`);

      const response = await fetch(searchUrl);
      if (!response.ok) return null;

      const html = await response.text();
      return this.parseExactItemSales(html, product.itemId, 'exact-title-search');

    } catch (error) {
      console.warn(`⚠️ Error searching by exact title for ${product.itemId}:`, error);
      return null;
    }
  }

  /**
   * 🎯 Parse eBay purchase history price format
   * Handles formats like: 'US $59.00', '$45.99', '€23.50'
   */
  parseEbayPrice(priceText) {
    if (!priceText) return null;

    try {
      // Extract numeric value from eBay price format
      const priceMatch = priceText.match(/[\d,]+\.?\d*/);
      if (priceMatch) {
        const price = parseFloat(priceMatch[0].replace(/,/g, ''));
        return isNaN(price) ? null : price;
      }
      return null;
    } catch {
      return null;
    }
  }

  /**
   * 🎯 CRITICAL: Parse eBay purchase history date format for ACCURATE demand analysis
   * Handles formats: '20 Jul 2025 at 1:13:59pm PDT', '20 Jul 2025', 'Jul 20, 2025'
   */
  parseEbayPurchaseDate(dateText) {
    if (!dateText) return null;

    try {
      console.log(`🎯 CRITICAL: Parsing date "${dateText}"`);

      // eBay format: "20 Jul 2025 at 1:13:59pm PDT"
      // Extract the date part before "at"
      let datePart = dateText.split(' at ')[0];

      if (datePart) {
        const date = new Date(datePart);
        if (!isNaN(date.getTime())) {
          const isoDate = date.toISOString().split('T')[0];
          console.log(`✅ CRITICAL: Successfully parsed "${dateText}" to "${isoDate}"`);
          return isoDate;
        }
      }

      // Fallback: try parsing the full string
      const date = new Date(dateText);
      if (!isNaN(date.getTime())) {
        const isoDate = date.toISOString().split('T')[0];
        console.log(`✅ CRITICAL: Fallback parsed "${dateText}" to "${isoDate}"`);
        return isoDate;
      }

      console.log(`❌ CRITICAL: Failed to parse date "${dateText}"`);
      return null;
    } catch (error) {
      console.log(`❌ CRITICAL: Error parsing date "${dateText}":`, error);
      return null;
    }
  }

  /**
   * 🎯 CRITICAL: Format date for display with time context
   * Shows readable date with recency indicators for demand analysis
   */
  formatDateWithContext(dateString) {
    if (!dateString) return 'Unknown date';

    try {
      const date = new Date(dateString);
      const now = new Date();
      const diffDays = Math.floor((now - date) / (1000 * 60 * 60 * 24));

      // Format the date
      const formatted = date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });

      // Add recency context for demand analysis
      if (diffDays === 0) return `${formatted} (today)`;
      if (diffDays === 1) return `${formatted} (yesterday)`;
      if (diffDays <= 7) return `${formatted} (${diffDays} days ago)`;
      if (diffDays <= 30) return `${formatted} (${Math.floor(diffDays/7)} weeks ago)`;
      if (diffDays <= 90) return `${formatted} (${Math.floor(diffDays/30)} months ago)`;

      return `${formatted} (${Math.floor(diffDays/30)} months ago)`;
    } catch {
      return dateString;
    }
  }

  /**
   * 🎯 CRITICAL: Parse eBay's purchase history using USER'S EXACT SELECTORS
   * Uses the proven selectors: table tr:not(:first-child) and tr td
   */
  parsePurchaseHistoryPage(html, itemId) {
    try {
      console.log(`🎯 CRITICAL: Parsing purchase history for item ${itemId} using EXACT USER SELECTORS`);

      // Create a temporary DOM to parse the HTML
      const parser = new DOMParser();
      const doc = parser.parseFromString(html, 'text/html');

      // 🎯 USER'S EXACT SELECTOR: Find the table
      const table = doc.querySelector('table');
      if (!table) {
        console.log(`❌ CRITICAL: No table found for item ${itemId}`);
        return null;
      }

      console.log(`✅ CRITICAL: Found table for item ${itemId}`);
      console.log(`📋 Table HTML preview:`, table.outerHTML.substring(0, 500));

      // 🎯 USER'S EXACT SELECTOR: Get all rows and skip header (index 0)
      const rows = table.querySelectorAll('tr');
      console.log(`📋 CRITICAL: Found ${rows.length} total rows (including header)`);

      const transactions = [];

      // Skip header row (index 0), process data rows (index 1+)
      for (let i = 1; i < rows.length; i++) {
        const row = rows[i];

        // 🎯 USER'S EXACT SELECTOR: Get all cells in this row
        const cells = row.querySelectorAll('td');

        console.log(`📋 Row ${i}: ${cells.length} cells`);

        // Log cell contents for debugging
        if (cells.length > 0) {
          const cellTexts = Array.from(cells).map(cell => cell.innerText.trim());
          console.log(`📋 Row ${i} cells:`, cellTexts);
        }

        // 🎯 USER'S EXACT STRUCTURE: Expect exactly 4 columns [userId, price, quantity, date]
        if (cells.length === 4) {
          const transaction = {
            userId: cells[0].innerText.trim(),
            price: cells[1].innerText.trim(),
            quantity: cells[2].innerText.trim(),
            date: cells[3].innerText.trim()
          };

          // Validate we have meaningful data
          if (transaction.userId && transaction.date) {
            transactions.push(transaction);
            console.log(`🎯 CRITICAL SUCCESS: Extracted transaction:`, transaction);
          } else {
            console.log(`⚠️ Row ${i} missing required data:`, transaction);
          }
        } else {
          console.log(`⚠️ Row ${i} has ${cells.length} cells, expected 4`);
        }
      }

      if (transactions.length === 0) {
        console.log(`❌ CRITICAL: No valid transactions found for item ${itemId}`);
        return null;
      }

      console.log(`🎯 CRITICAL SUCCESS: Found ${transactions.length} transactions with REAL DATES for item ${itemId}`);

      // Log all extracted transactions
      transactions.forEach((t, index) => {
        console.log(`Transaction ${index + 1}: User ${t.userId}, ${t.quantity}x @ ${t.price} on ${t.date}`);
      });

      // 🎯 BULLETPROOF: Use EXACT console command logic - no complex parsing
      const salesHistory = transactions.map(t => {
        return {
          userId: t.userId,
          price: this.parseEbayPrice(t.price),
          quantity: parseInt(t.quantity) || 1,
          date: t.date, // Keep original eBay date format: "20 Jul 2025 at 2:06:55pm PDT"
          dateText: t.date, // Same as date for display
          originalDate: t.date, // Keep original for debugging
          formattedDate: t.date, // Use original date directly - let UI handle formatting
          priceText: t.price,
          source: 'direct-purchase-history-table'
        };
      });

      // Calculate totals
      const totalQuantity = salesHistory.reduce((sum, s) => sum + s.quantity, 0);
      const totalTransactions = salesHistory.length;

      // Sort by date (newest first)
      salesHistory.sort((a, b) => new Date(b.date || 0) - new Date(a.date || 0));

      // Calculate metrics
      const lastSoldDate = salesHistory.length > 0 ? salesHistory[0].date : null;
      const salesVelocity = this.calculateSalesVelocity(totalQuantity, salesHistory);
      const demandScore = this.calculateDemandScore(totalQuantity);

      // Calculate average sale price
      const pricesWithValues = salesHistory.filter(s => s.price && s.price > 0);
      const averageSalePrice = pricesWithValues.length > 0
        ? Math.round((pricesWithValues.reduce((sum, s) => sum + s.price, 0) / pricesWithValues.length) * 100) / 100
        : null;

      console.log(`✅ EXTRACTED ${totalTransactions} transactions for item ${itemId}: ${totalQuantity} total quantity sold`);

      return {
        salesFrequency: totalQuantity,
        salesHistory: salesHistory, // Return ALL transactions, not limited
        lastSoldDate,
        averageSalePrice,
        salesVelocity,
        demandScore,
        isSoldListing: false,
        extractedFrom: 'direct-purchase-history-table',
        itemId: itemId,
        totalTransactions: totalTransactions,
        purchaseHistoryUrl: `https://www.ebay.com/bin/purchaseHistory?item=${itemId}`,
        timestamp: Date.now()
      };

    } catch (error) {
      console.warn(`⚠️ Error parsing purchase history for item ${itemId}:`, error);
      return null;
    }
  }

  /**
   * Extract purchase data from table row cells
   * 🎯 SIMPLIFIED: Now that we use direct table parsing, this is a backup method
   */
  extractPurchaseRowData(cells) {
    try {
      // This is now a backup method since we use direct table parsing
      // eBay purchase history table structure: [userId, price, quantity, date]

      if (cells.length >= 4) {
        const userId = cells[0]?.textContent?.trim();
        const priceText = cells[1]?.textContent?.trim();
        const quantityText = cells[2]?.textContent?.trim();
        const dateText = cells[3]?.textContent?.trim();

        const quantity = parseInt(quantityText) || 1;
        const price = this.parseEbayPrice(priceText);
        const date = this.parseEbayPurchaseDate(dateText);

        if (date && userId) {
          return {
            userId,
            date,
            quantity,
            price,
            source: 'purchase-history-table-backup'
          };
        }
      }

      return null;
    } catch (error) {
      return null;
    }
  }

  /**
   * Extract purchase data from purchase item elements
   */
  extractPurchaseItemData(element) {
    try {
      const text = element.textContent;

      // Look for date patterns
      const date = this.extractDateFromText(text);
      if (!date) return null;

      // Look for quantity
      let quantity = 1;
      const qtyMatch = text.match(/qty:?\s*(\d+)|quantity:?\s*(\d+)|(\d+)\s*sold/i);
      if (qtyMatch) {
        quantity = parseInt(qtyMatch[1] || qtyMatch[2] || qtyMatch[3]) || 1;
      }

      // Look for price
      let price = null;
      const priceMatch = text.match(/\$?([\d,]+\.?\d*)/);
      if (priceMatch) {
        price = parseFloat(priceMatch[1].replace(/,/g, ''));
      }

      return {
        date,
        quantity,
        price,
        source: 'purchase-history-item'
      };
    } catch (error) {
      return null;
    }
  }

  /**
   * Check if a string looks like a date
   */
  isDateString(str) {
    // Check for common date patterns
    const datePatterns = [
      /\d{1,2}\/\d{1,2}\/\d{2,4}/, // MM/DD/YYYY
      /\d{1,2}-\d{1,2}-\d{2,4}/, // MM-DD-YYYY
      /[A-Za-z]{3}\s+\d{1,2},?\s+\d{4}/, // Jan 15, 2024
      /\d{1,2}\s+[A-Za-z]{3}\s+\d{4}/, // 15 Jan 2024
      /\d{4}-\d{2}-\d{2}/ // YYYY-MM-DD
    ];

    return datePatterns.some(pattern => pattern.test(str));
  }

  /**
   * Extract date from text using various patterns
   */
  extractDateFromText(text) {
    // Try multiple date extraction patterns
    const patterns = [
      /([A-Za-z]{3}\s+\d{1,2},?\s+\d{4})/,
      /(\d{1,2}\/\d{1,2}\/\d{2,4})/,
      /(\d{1,2}-\d{1,2}-\d{2,4})/,
      /(\d{1,2}\s+[A-Za-z]{3}\s+\d{4})/
    ];

    for (const pattern of patterns) {
      const match = text.match(pattern);
      if (match) {
        return this.parseSoldDate(match[1]);
      }
    }

    return null;
  }

  /**
   * 🎯 Parse exact item sales from search results
   * This focuses on finding the specific item, not similar products
   */
  parseExactItemSales(html, itemId, searchMethod) {
    try {
      console.log(`🔍 Parsing exact item sales for ${itemId} using ${searchMethod}`);

      const parser = new DOMParser();
      const doc = parser.parseFromString(html, 'text/html');

      // Find sold listing containers
      const containers = doc.querySelectorAll('li.s-card.s-card--horizontal, li.s-card, li[class*="s-item"]');

      const salesHistory = [];
      let totalSales = 0;
      let totalValue = 0;

      for (const container of containers) {
        const soldData = this.extractSoldItemData(container);
        if (soldData && soldData.price > 0) {
          // For exact searches, we're more confident these are the right items
          salesHistory.push({
            ...soldData,
            source: searchMethod,
            verified: true
          });
          totalSales++;
          totalValue += soldData.price;
        }
      }

      if (totalSales === 0) {
        console.log(`📭 No exact sales found for item ${itemId} using ${searchMethod}`);
        return null;
      }

      // Sort by date (newest first)
      salesHistory.sort((a, b) => new Date(b.date || 0) - new Date(a.date || 0));

      // Calculate metrics
      const averageSalePrice = totalSales > 0 ? Math.round((totalValue / totalSales) * 100) / 100 : null;
      const lastSoldDate = salesHistory.length > 0 ? salesHistory[0].date : null;
      const salesVelocity = this.calculateSalesVelocity(totalSales, salesHistory);
      const demandScore = this.calculateDemandScore(totalSales);

      console.log(`✅ Exact item sales for ${itemId}: ${totalSales} sales found, avg price: $${averageSalePrice}`);

      return {
        salesFrequency: totalSales,
        salesHistory: salesHistory.slice(0, 10), // Keep last 10 sales
        lastSoldDate,
        averageSalePrice,
        salesVelocity,
        demandScore,
        isSoldListing: false,
        extractedFrom: searchMethod,
        itemId: itemId,
        purchaseHistoryUrl: `https://www.ebay.com/bin/purchaseHistory?item=${itemId}`,
        verificationNote: 'Click verification link to manually check purchase history',
        timestamp: Date.now()
      };

    } catch (error) {
      console.warn(`⚠️ Error parsing exact item sales for ${itemId}:`, error);
      return null;
    }
  }

  /**
   * Parse sold listings page HTML to extract sales data
   */
  parseSoldListingsPage(html, originalProduct) {
    try {
      // Create a temporary DOM to parse the HTML
      const parser = new DOMParser();
      const doc = parser.parseFromString(html, 'text/html');

      // Find sold listing containers
      const containers = doc.querySelectorAll('li.s-card.s-card--horizontal, li.s-card, li[class*="s-item"]');

      const salesHistory = [];
      let totalSales = 0;
      let totalValue = 0;

      for (const container of containers) {
        const soldData = this.extractSoldItemData(container);
        if (soldData && soldData.price > 0) {
          salesHistory.push(soldData);
          totalSales++;
          totalValue += soldData.price;
        }
      }

      // Calculate metrics
      const averageSalePrice = totalSales > 0 ? Math.round((totalValue / totalSales) * 100) / 100 : null;
      const lastSoldDate = salesHistory.length > 0 ? salesHistory[0].date : null;
      const salesVelocity = this.calculateSalesVelocity(totalSales, salesHistory);
      const demandScore = this.calculateDemandScore(totalSales);

      console.log(`📊 Sales analysis for "${originalProduct.title?.substring(0, 30)}...": ${totalSales} sales found`);

      return {
        salesFrequency: totalSales,
        salesHistory: salesHistory.slice(0, 10), // Keep last 10 sales
        lastSoldDate,
        averageSalePrice,
        salesVelocity,
        demandScore,
        isSoldListing: false,
        extractedFrom: 'background-search',
        searchTitle: this.cleanTitleForSearch(originalProduct.title),
        timestamp: Date.now()
      };

    } catch (error) {
      console.warn('⚠️ Error parsing sold listings page:', error);
      return this.getEmptySalesData();
    }
  }

  /**
   * Extract data from a single sold item container
   */
  extractSoldItemData(container) {
    try {
      const titleElement = container.querySelector('.s-card__title, .s-item__title');
      const priceElement = container.querySelector('.s-card__price, .s-item__price');
      const dateElement = container.querySelector('.s-card__ended-date, .s-item__ended-date, .NEGATIVE');

      const title = titleElement?.textContent?.trim();
      const priceText = priceElement?.textContent?.trim();
      const dateText = dateElement?.textContent?.trim();

      if (!title || !priceText) return null;

      // Extract price
      const priceMatch = priceText.match(/\$?([\d,]+\.?\d*)/);
      const price = priceMatch ? parseFloat(priceMatch[1].replace(/,/g, '')) : 0;

      // Extract date
      const date = this.parseSoldDate(dateText);

      return {
        title: title.substring(0, 100),
        price,
        date,
        dateText
      };
    } catch (error) {
      return null;
    }
  }

  /**
   * Parse sold date from eBay formats
   */
  parseSoldDate(dateText) {
    if (!dateText) return null;
    
    try {
      // Handle relative dates (3d 2h, 1w 2d, etc.)
      const relativeMatch = dateText.match(/(\d+)([dwh])/g);
      if (relativeMatch) {
        let daysAgo = 0;
        for (const match of relativeMatch) {
          const [, num, unit] = match.match(/(\d+)([dwh])/);
          const value = parseInt(num);
          if (unit === 'd') daysAgo += value;
          else if (unit === 'w') daysAgo += value * 7;
          else if (unit === 'h') daysAgo += value / 24;
        }
        const soldDate = new Date();
        soldDate.setDate(soldDate.getDate() - daysAgo);
        return soldDate.toISOString().split('T')[0];
      }
      
      // Handle absolute dates (Mar 15, 2024)
      const absoluteMatch = dateText.match(/([A-Za-z]{3})\s+(\d{1,2}),?\s+(\d{4})/);
      if (absoluteMatch) {
        const [, month, day, year] = absoluteMatch;
        const date = new Date(`${month} ${day}, ${year}`);
        return date.toISOString().split('T')[0];
      }
      
      return null;
    } catch (error) {
      return null;
    }
  }

  /**
   * Calculate sales velocity from sales history
   */
  calculateSalesVelocity(totalSales, salesHistory) {
    if (totalSales === 0) return 0;
    if (salesHistory.length === 0) return 'Unknown';
    
    try {
      // Find date range
      const dates = salesHistory.map(sale => new Date(sale.date)).filter(d => !isNaN(d));
      if (dates.length === 0) return 'Unknown';
      
      const oldestDate = new Date(Math.min(...dates));
      const newestDate = new Date(Math.max(...dates));
      const daysDiff = (newestDate - oldestDate) / (1000 * 60 * 60 * 24);
      
      if (daysDiff <= 0) return totalSales; // All sold same day
      
      const monthsDiff = daysDiff / 30;
      return Math.round((totalSales / monthsDiff) * 10) / 10;
    } catch (error) {
      return 'Unknown';
    }
  }

  /**
   * Calculate demand score based on sales frequency
   */
  calculateDemandScore(soldCount) {
    if (!soldCount || soldCount === 0) return 0;
    
    if (soldCount <= 5) return Math.min(2, soldCount);
    if (soldCount <= 15) return Math.min(5, 2 + Math.floor(soldCount / 3));
    if (soldCount <= 50) return Math.min(8, 5 + Math.floor(soldCount / 10));
    return Math.min(10, 8 + Math.floor(soldCount / 25));
  }

  /**
   * Get cache key for a product
   */
  getCacheKey(product) {
    const cleanTitle = this.cleanTitleForSearch(product.title);
    return `sales_${cleanTitle.replace(/\s+/g, '_').toLowerCase()}`;
  }

  /**
   * Utility delay function
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Return empty sales data structure
   */
  getEmptySalesData() {
    return {
      salesFrequency: 0,
      salesHistory: [],
      lastSoldDate: null,
      averageSalePrice: null,
      salesVelocity: 0,
      demandScore: 0,
      isSoldListing: false,
      extractedFrom: 'background-search-failed',
      timestamp: Date.now()
    };
  }
}

// Make globally available
window.SalesHistoryFetcher = SalesHistoryFetcher;
