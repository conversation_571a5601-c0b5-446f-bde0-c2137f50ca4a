/**
 * ✅ UNIVERSAL EBAY HANDLER - Handles basic messages on all eBay pages
 * Loads at document_start for immediate availability
 * Based on EbayLister4 reference pattern
 */

(function() {
  'use strict';
  
  console.log('🌐 Universal eBay Handler loading at document_start...');
  
  /**
   * Handle basic messages that don't require complex dependencies
   */
  function handleMessage(message, sender, sendResponse) {
    console.log('📨 Universal eBay Handler received message:', message.action);
    
    try {
      // Handle ping requests
      if (message.action === 'ping') {
        console.log('🏓 Universal handler responding to ping');
        sendResponse({
          success: true,
          message: 'pong',
          handler: 'universal-ebay-handler',
          url: window.location.href,
          timestamp: Date.now()
        });
        return true;
      }
      
      // Handle content script readiness check
      if (message.action === 'checkContentScript') {
        console.log('✅ Universal handler confirming ready');
        sendResponse({
          success: true,
          ready: true,
          handler: 'universal-ebay-handler',
          url: window.location.href
        });
        return true;
      }
      
      // Handle startScraping directly as universal fallback
      if (message.action === 'startScraping') {
        console.log('🔄 Universal handler: Handling startScraping directly as fallback');
        console.log('🔍 Universal handler: Message details:', message);
        console.log('🔍 Universal handler: Current URL:', window.location.href);
        console.log('🔍 Universal handler: Page title:', document.title);
        console.log('🔍 Universal handler: Document ready state:', document.readyState);

        // ✅ CRITICAL FIX: Proper async handling with sendResponse in scope
        handleScrapingWithTimeout(message, sendResponse);
        return true; // Keep message channel open
      }

      // ✅ FIXED: Always handle startScraping in universal handler
      // The search handler delegation was causing the scraping to fail
      console.log('🔄 Universal handler: All messages handled by universal handler for reliability');
      


      // Handle testSelectors for debugging
      if (message.action === 'testSelectors') {
        console.log('🧪 Testing eBay selectors...');
        const testResults = testEbaySelectors();
        sendResponse({
          success: true,
          results: testResults,
          handler: 'universal-ebay-handler'
        });
        return true;
      }

      // Handle other basic messages
      console.log('❓ Universal handler - unhandled message:', message.action);
      sendResponse({
        success: false,
        error: 'Message not handled by universal handler',
        action: message.action,
        handler: 'universal-ebay-handler',
        suggestion: 'This message might need the search page handler'
      });
      
    } catch (error) {
      console.error('❌ Universal handler error:', error);
      sendResponse({
        success: false,
        error: 'Universal handler crashed: ' + error.message,
        action: message.action
      });
    }
    
    return true;
  }
  
  /**
   * ✅ CRITICAL FIX: Proper async scraping handler with timeout protection
   */
  async function handleScrapingWithTimeout(message, sendResponse) {
    console.log('🚀 Starting scraping with timeout protection...');

    // Set up timeout protection
    const timeoutId = setTimeout(() => {
      console.error('⏰ Scraping timeout after 12 seconds');
      sendResponse({
        success: false,
        error: 'Scraping timeout after 12 seconds',
        products: [],
        handler: 'universal-ebay-handler-timeout'
      });
    }, 12000); // 12 second timeout (less than service worker's 15s)

    try {
      // Quick selector test first - UPDATED TO CURRENT EBAY STRUCTURE
      console.log('🔍 Quick selector test with current eBay selectors...');

      // Try multiple selectors to catch all product containers
      const selectors = [
        'li.s-card.s-card--horizontal',
        'li.s-card',
        'li[class*="s-card"]',
        'li[class*="s-item"]', // Alternative eBay structure
        '.s-item', // Direct class match
        '[data-testid*="item"]' // Test ID based
      ];

      let testElements = [];
      let usedSelector = '';

      for (const selector of selectors) {
        const elements = document.querySelectorAll(selector);
        console.log(`🔍 Testing "${selector}": ${elements.length} elements found`);
        if (elements.length >= testElements.length) {
          testElements = elements;
          usedSelector = selector;
        }
      }

      // Additional debug: check for any missed containers
      const allProductLinks = document.querySelectorAll('a[href*="/itm/"]');
      console.log(`🔍 Total product links found: ${allProductLinks.length}`);
      console.log(`🔍 Selected ${testElements.length} containers with "${usedSelector}"`);

      if (allProductLinks.length > testElements.length) {
        console.log(`⚠️ Potential missed products: ${allProductLinks.length - testElements.length}`);
      }

      console.log(`🔍 Using best selector "${usedSelector}" with ${testElements.length} elements`);

      if (testElements.length === 0) {
        clearTimeout(timeoutId);
        console.log('❌ No products found - page may not be loaded or selectors changed');
        sendResponse({
          success: true,
          products: [],
          error: 'No products found on page',
          handler: 'universal-ebay-handler',
          debug: {
            url: window.location.href,
            title: document.title,
            readyState: document.readyState,
            totalElements: document.querySelectorAll('*').length
          }
        });
        return;
      }

      // Extract products using streamlined logic
      console.log(`🚀 Extracting products from ${testElements.length} elements using selector "${usedSelector}"...`);
      const products = await worldClassExtraction(Array.from(testElements));

      clearTimeout(timeoutId);
      console.log(`✅ Extraction completed: ${products.length} products`);

      sendResponse({
        success: true,
        products: products,
        handler: 'universal-ebay-handler',
        action: message.action,
        totalFound: products.length,
        url: window.location.href,
        timestamp: Date.now()
      });

    } catch (error) {
      clearTimeout(timeoutId);
      console.error('❌ Scraping failed:', error);
      sendResponse({
        success: false,
        error: 'Scraping failed: ' + error.message,
        products: [],
        handler: 'universal-ebay-handler',
        stack: error.stack
      });
    }
  }

  /**
   * 🚀 WORLD-CLASS EXTRACTION - Single unified extraction logic with comprehensive data
   */
  async function worldClassExtraction(listings) {
    console.log(`🚀 World-class extraction processing ${listings.length} listings...`);

    const products = [];
    const maxToProcess = Math.min(listings.length, 60); // Process all available products

    for (let i = 0; i < maxToProcess; i++) {
      try {
        const listing = listings[i];

        // Quick validation
        if (!listing || !listing.querySelector) {
          console.warn(`⚠️ Invalid listing element at index ${i}`);
          continue;
        }

        // 🚀 CURRENT EBAY SELECTORS - Based on actual li.s-card structure

        // Extract core data - FIXED: Based on actual eBay HTML structure

        // The structure is: <a class="su-link"><div class="s-card__title"><span>Title</span></div></a>
        const titleElement = listing.querySelector('.s-card__title');
        const linkElement = listing.querySelector('a.su-link[href*="/itm/"]'); // The actual link element

        const title = titleElement ? titleElement.innerText.trim() : null;
        const url = linkElement ? linkElement.href : null;

        // DEBUG: Log first few extractions
        if (i < 3) {
          console.log(`🔍 Product ${i}: title="${title}" url="${url ? 'FOUND' : 'MISSING'}"`);
        }
        const price = extractCardPrice(listing);
        const shipping = extractCardShipping(listing);
        const condition = extractCardCondition(listing);
        const imageUrl = extractCardImage(listing);

        // Debug image extraction for first few products
        if (i < 3) {
          console.log(`🖼️ Image debug for product ${i + 1}:`, {
            imageUrl: imageUrl,
            hasImage: !!imageUrl,
            allImages: Array.from(listing.querySelectorAll('img')).map(img => ({
              src: img.src,
              className: img.className,
              alt: img.alt
            }))
          });
        }
        const soldDate = extractText(listing, '.s-card__ended-date');
        const seller = extractCardSeller(listing);
        const sellerFeedback = extractCardSellerFeedback(listing);
        const listingId = extractListingId(listing);
        const badges = extractCardBadges(listing);

        // Build data object with extracted values
        const data = {
          index: i,
          extractedAt: new Date().toISOString(),

          // Core product data
          title: title,
          url: url,
          link: url, // UI expects 'link' field for clickable URLs
          price: price,
          shipping: shipping,
          condition: condition,
          imageUrl: imageUrl,
          soldDate: soldDate,

          // Current eBay seller info
          seller: seller,
          sellerFeedback: sellerFeedback,
          listingId: listingId,
          itemId: listingId, // UI expects 'itemId' field for display
          badges: badges,

          // Enhanced fields for rich UI display
          location: extractCardLocation(listing),
          bids: extractCardBids(listing),
          listingType: extractListingType(listing),
          bestOfferAccepted: price && typeof price === 'string' && price.includes('Best Offer'),
          freeReturns: badges.includes('Free returns'),
          category: extractText(listing, '.s-card__subtitle'),
          charity: badges.some(badge => badge.toLowerCase().includes('charity'))
        };

        // Skip promotional items
        if (data.title && data.title.includes('Shop on eBay')) {
          console.log(`⏭️ Skipping promotional item ${i}: "${data.title}"`);
          continue;
        }

        // Validate minimum required data
        if (!data.title || !data.url) {
          console.log(`⚠️ Skipping item ${i}: Missing required data (title: ${!!data.title}, url: ${!!data.url})`);
          continue;
        }

        // Calculate data quality
        data.extractionQuality = calculateQuality(data);
        data.source = 'universal-handler-world-class';

        // Debug logging for first few products to check data completeness
        if (i < 3) {
          console.log(`🔍 DEBUG Product ${i + 1} data:`, {
            title: !!data.title,
            price: !!data.price,
            url: !!data.url,
            itemId: !!data.itemId,
            condition: !!data.condition,
            seller: !!data.seller,
            imageUrl: !!data.imageUrl,
            imageUrlValue: data.imageUrl, // Show actual image URL
            shipping: !!data.shipping,
            sellerFeedback: !!data.sellerFeedback,
            badges: data.badges?.length || 0
          });
        }

        console.log(`📦 PRODUCT ${i + 1}: "${data.title}" - $${data.price} - ${data.condition} (${data.extractionQuality}% complete)`);
        products.push(data);

      } catch (error) {
        console.warn(`⚠️ Failed to extract product ${i}:`, error);
      }

      // Progress logging every 10 items
      if ((i + 1) % 10 === 0) {
        console.log(`📊 Progress: ${i + 1}/${maxToProcess} products processed, ${products.length} valid products found`);
      }
    }

    console.log(`✅ World-class extraction completed: ${products.length}/${listings.length} products extracted`);
    return products;
  }

  // Helper extraction functions
  function extractText(container, selector) {
    try {
      const element = container.querySelector(selector);
      return element ? element.textContent.trim() : null;
    } catch (error) {
      return null;
    }
  }

  function extractAttribute(container, selector, attribute) {
    try {
      const element = container.querySelector(selector);
      return element ? element.getAttribute(attribute) : null;
    } catch (error) {
      return null;
    }
  }

  // 🚀 CURRENT EBAY CARD EXTRACTION FUNCTIONS

  function extractCardPrice(container) {
    const priceElement = container.querySelector('.s-card__price');
    if (!priceElement) return null;

    // Price can be complex (e.g., "$47.49Was: $49.99"). We only want the primary price.
    let price = priceElement.innerText.split(' ')[0].trim();

    // Handle cases where there was a best offer instead of a fixed price
    const bestOfferElement = container.querySelector('.s-card__price--accepted');
    if (bestOfferElement) {
      price = bestOfferElement.innerText.trim();
    }

    // Extract numeric value
    const match = price.match(/[\d,]+\.?\d*/);
    return match ? parseFloat(match[0].replace(/,/g, '')) : null;
  }

  function extractCardShipping(container) {
    // Find all elements with the shipping selector class
    const shippingElements = container.querySelectorAll('span.su-styled-text.secondary.large');

    let shippingElement = null;

    // Look for the one that contains delivery/shipping text
    for (const element of shippingElements) {
      const text = element.textContent.trim().toLowerCase();
      if (text.includes('delivery') || text.includes('shipping') || text.includes('free')) {
        shippingElement = element;
        break;
      }
    }

    // Fallback to legacy selectors if no delivery text found
    if (!shippingElement) {
      const fallbackSelectors = ['.s-card__shipping', '.s-card__delivery-cost'];
      for (const selector of fallbackSelectors) {
        shippingElement = container.querySelector(selector);
        if (shippingElement && shippingElement.textContent.trim()) {
          break;
        }
      }
    }

    if (!shippingElement) return null;

    const shippingText = shippingElement.textContent.trim();

    if (shippingText.toLowerCase().includes('free')) {
      return 0;  // Return 0 for free shipping
    } else {
      // Extracts the price, e.g., "+$7.79 delivery" -> 7.79
      const match = shippingText.match(/\$(\d+\.?\d*)/);
      return match ? parseFloat(match[1]) : null;
    }
  }

  function extractCardCondition(container) {
    const conditionElement = container.querySelector('.s-card__subtitle');
    if (!conditionElement) return null;

    // The condition is often the first part of the subtitle text.
    return conditionElement.innerText.split('·')[0].trim();
  }

  function extractCardImage(container) {
    // Try multiple image selectors
    const imageSelectors = [
      'img.s-card__image-img',
      'img.s-item__image',
      'img[src*="ebayimg"]',
      'img[src*="i.ebayimg"]',
      '.s-card__image img',
      '.s-item__image img',
      'img'
    ];

    let imageElement = null;
    for (const selector of imageSelectors) {
      imageElement = container.querySelector(selector);
      if (imageElement && imageElement.src && !imageElement.src.includes('data:')) {
        break;
      }
    }

    if (!imageElement || !imageElement.src) return null;

    let imageUrl = imageElement.src;

    // eBay often uses low-res thumbnails in search. Replace for high-res version.
    if (imageUrl.includes('s-l225')) {
      imageUrl = imageUrl.replace('s-l225', 's-l1600');
    } else if (imageUrl.includes('s-l300')) {
      imageUrl = imageUrl.replace('s-l300', 's-l1600');
    } else if (imageUrl.includes('s-l140')) {
      imageUrl = imageUrl.replace('s-l140', 's-l1600');
    } else if (imageUrl.includes('s-l64')) {
      imageUrl = imageUrl.replace('s-l64', 's-l1600');
    }

    return imageUrl;
  }

  function extractCardSeller(container) {
    const sellerElement = container.querySelector('.s-card__seller-info-text');
    if (!sellerElement) return null;

    return sellerElement.innerText.split(' ')[0].trim();
  }

  function extractCardSellerFeedback(container) {
    const sellerElement = container.querySelector('.s-card__seller-info-text');
    if (!sellerElement) return null;

    const match = sellerElement.innerText.match(/(\d+\.\d+%|\d+%)/);
    return match ? match[0] : null;
  }

  function extractListingId(container) {
    // Try multiple methods to extract eBay item ID

    // Method 1: Check data attributes
    if (container.dataset.listingid) {
      return container.dataset.listingid;
    }

    // Method 2: Extract from URL
    const linkElement = container.querySelector('a.su-link[href*="/itm/"]');
    if (linkElement && linkElement.href) {
      const urlMatch = linkElement.href.match(/\/itm\/([^\/\?]+)/);
      if (urlMatch) {
        return urlMatch[1];
      }
    }

    // Method 3: Check for any data-* attributes that might contain item ID
    for (const attr of container.attributes) {
      if (attr.name.includes('item') || attr.name.includes('listing')) {
        return attr.value;
      }
    }

    return null;
  }

  function extractCardBadges(container) {
    const badges = [];
    const badgeElements = container.querySelectorAll('.s-card__badge-icon');
    badgeElements.forEach(badge => {
      const badgeText = badge.innerText.trim();
      if (badgeText) {
        badges.push(badgeText);
      }
    });
    return badges;
  }

  function extractCardLocation(container) {
    // Look for location in the secondary large text elements
    const locationElements = container.querySelectorAll('span.su-styled-text.secondary.large');

    for (const element of locationElements) {
      const text = element.textContent.trim();
      if (text.toLowerCase().includes('located in') || text.toLowerCase().includes('from')) {
        return text.replace(/^(Located in|From)\s*/i, '');
      }
    }

    return null;
  }

  function extractCardBids(container) {
    // Look for bid information in secondary large text
    const bidElements = container.querySelectorAll('span.su-styled-text.secondary.large');

    for (const element of bidElements) {
      const text = element.textContent.trim();
      if (text.toLowerCase().includes('bid')) {
        return text;
      }
    }

    return null;
  }

  function extractListingType(container) {
    // Check for auction indicators
    const bidElements = container.querySelectorAll('span.su-styled-text.secondary.large');

    for (const element of bidElements) {
      const text = element.textContent.trim().toLowerCase();
      if (text.includes('bid')) {
        return 'Auction';
      }
    }

    // Check for Buy It Now indicators
    const priceElement = container.querySelector('.s-card__price');
    if (priceElement) {
      const priceText = priceElement.textContent.toLowerCase();
      if (priceText.includes('buy it now') || !priceText.includes('bid')) {
        return 'Buy It Now';
      }
    }

    return 'Buy It Now'; // Default assumption
  }

  // Legacy extraction functions for compatibility
  function extractPrice(container) {
    return extractCardPrice(container);
  }

  function extractShipping(container) {
    return extractCardShipping(container);
  }

  function extractImage(container) {
    return extractCardImage(container);
  }

  function extractBids(container) {
    const bidsText = extractText(container, 'span.s-item__bids');
    if (!bidsText) return null;

    const bidMatch = bidsText.match(/(\d+)\s*(bid|bids)/i);
    if (bidMatch) return { count: parseInt(bidMatch[1]), type: 'bids' };

    const watcherMatch = bidsText.match(/(\d+)\s*(watcher|watchers)/i);
    if (watcherMatch) return { count: parseInt(watcherMatch[1]), type: 'watchers' };

    return null;
  }

  function extractBoolean(container, selector) {
    try {
      return container.querySelector(selector) !== null;
    } catch (error) {
      return false;
    }
  }

  function extractFreeReturns(container) {
    const returnsElement = container.querySelector('.s-item__free-returns, .s-item__returns-accepted');
    if (!returnsElement) return false;

    const text = returnsElement.textContent?.toLowerCase() || '';
    return text.includes('free') || text.includes('return');
  }

  function calculateQuality(data) {
    // Core required fields (must have)
    const coreFields = ['title', 'price', 'url', 'itemId'];
    const coreScore = coreFields.filter(field => data[field] !== null && data[field] !== '').length / coreFields.length;

    // Enhanced fields (nice to have)
    const enhancedFields = ['condition', 'seller', 'imageUrl', 'shipping', 'sellerFeedback', 'badges'];
    const enhancedScore = enhancedFields.filter(field => data[field] !== null && data[field] !== '').length / enhancedFields.length;

    // Weighted calculation: 70% core + 30% enhanced
    const totalScore = (coreScore * 0.7) + (enhancedScore * 0.3);
    return Math.round(totalScore * 100);
  }

  /**
   * Test eBay selectors for debugging
   */
  function testEbaySelectors() {
    console.log('🧪 Testing CURRENT eBay selectors...');

    // Test multiple container selectors to find the best one
    const containerSelectors = [
      'li.s-card.s-card--horizontal',
      'li.s-card',
      'li[class*="s-card"]'
    ];

    let bestContainerSelector = containerSelectors[0];
    let maxContainers = 0;

    containerSelectors.forEach(selector => {
      const count = document.querySelectorAll(selector).length;
      console.log(`🔍 Container test "${selector}": ${count} elements`);
      if (count > maxContainers) {
        maxContainers = count;
        bestContainerSelector = selector;
      }
    });

    const selectors = {
      containers: bestContainerSelector,
      title: '.s-card__title',
      link: 'a.su-link[href*="/itm/"]', // The actual link element
      price: '.s-card__price',
      condition: '.s-card__subtitle',
      seller: '.s-card__seller-info-text',
      image: 'img.s-card__image-img',
      shipping: '.su-styled-text.secondary.large, .s-card__shipping, .s-card__delivery-cost',
      badges: '.s-card__badge-icon'
    };

    const results = {};

    Object.entries(selectors).forEach(([name, selector]) => {
      const elements = document.querySelectorAll(selector);
      results[name] = {
        selector: selector,
        found: elements.length,
        sample: elements.length > 0 ? elements[0].textContent?.trim().substring(0, 100) : null
      };
      console.log(`🔍 ${name}: ${elements.length} elements found`);
    });

    return results;
  }

  /**
   * Check if current page is a search page
   */
  function isSearchPage() {
    const url = window.location.href;
    return url.includes('/sch/i.html') && url.includes('_nkw=');
  }
  
  /**
   * Check if message is search-specific
   */
  function isSearchSpecificMessage(action) {
    const searchActions = [
      'startScraping',
      'testSelectors', 
      'minimalTest',
      'scanCurrentPage'
    ];
    return searchActions.includes(action);
  }
  
  // ✅ CRITICAL: Register message listener immediately
  chrome.runtime.onMessage.addListener(handleMessage);
  
  // Send ready notification to service worker
  function notifyServiceWorker() {
    chrome.runtime.sendMessage({
      action: 'contentScriptReady',
      handler: 'universal-ebay-handler',
      url: window.location.href,
      timestamp: Date.now(),
      isSearchPage: isSearchPage()
    }).catch(error => {
      console.log('📡 Service worker not ready yet for universal handler');
    });
  }
  
  // Notify service worker when ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', notifyServiceWorker);
  } else {
    notifyServiceWorker();
  }
  
  // Global status for debugging
  window.universalEbayHandler = {
    isReady: () => true,
    getUrl: () => window.location.href,
    isSearchPage: isSearchPage
  };
  
  console.log('✅ Universal eBay Handler setup complete');
  
})();
